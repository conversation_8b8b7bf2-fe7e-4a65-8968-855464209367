[2025-06-03 10:53:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:53:28] production.ERROR: Uncaught Error: Call to undefined method NPA\ACPMS\Http\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught Error: Call to undefined method NPA\\ACPMS\\Http\\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:53:44] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:54:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:55:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:56:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:57:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:58:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:59:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:00:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:01:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:02:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:03:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:04:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:05:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:06:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:07:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:08:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:09:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:10:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:11:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:12:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:13:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:14:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:15:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:16:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:17:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:18:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:19:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:20:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:21:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:22:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:23:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:24:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:25:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:26:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:27:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:28:36] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:29:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:30:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:31:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:32:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:33:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:34:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:35:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:36:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:37:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:38:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:39:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:40:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:41:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:42:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:43:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:44:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:45:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:46:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:47:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:48:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:49:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:50:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:51:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:52:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:53:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:54:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:55:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:56:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:57:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:58:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:59:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:00:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:01:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:02:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:03:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:04:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:05:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:06:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:07:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:08:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:09:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:10:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:11:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:12:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:13:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:14:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:15:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:16:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:17:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:18:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:19:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:20:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:21:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:22:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:23:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:24:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:25:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:26:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:27:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:28:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:28:40] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'acpms.roles' doesn't exist (SQL: insert into `roles` (`name`, `da_name`, `can_be_acted`, `can_act`, `created_at`, `updated_at`) values (cpms-assigner-publisher, آمر اعطا و ناشر قراردادها, 0, 1, 2025-06-03 12:28:40, 2025-06-03 12:28:40)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'acpms.roles' doesn't exist (SQL: insert into `roles` (`name`, `da_name`, `can_be_acted`, `can_act`, `created_at`, `updated_at`) values (cpms-assigner-publisher, آمر اعطا و ناشر قراردادها, 0, 1, 2025-06-03 12:28:40, 2025-06-03 12:28:40)) at C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:664, Doctrine\\DBAL\\Driver\\PDO\\Exception(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'acpms.roles' doesn't exist at C:\\0.projects\\WebApp\\acpms\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDO\\Exception.php:18, PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'acpms.roles' doesn't exist at C:\\0.projects\\WebApp\\acpms\\vendor\\doctrine\\dbal\\lib\\Doctrine\\DBAL\\Driver\\PDOConnection.php:82)
[stacktrace]
#0 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(624): Illuminate\\Database\\Connection->runQueryCallback('insert into `ro...', Array, Object(Closure))
#1 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(459): Illuminate\\Database\\Connection->run('insert into `ro...', Array, Object(Closure))
#2 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(411): Illuminate\\Database\\Connection->statement('insert into `ro...', Array)
#3 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2142): Illuminate\\Database\\Connection->insert('insert into `ro...', Array)
#4 C:\\0.projects\\WebApp\\acpms\\database\\migrations\\2024_01_01_000000_add_cpms_assigner_publisher_role.php(22): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(359): AddCpmsAssignerPublisherRole->up()
#6 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(366): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#7 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(177): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(AddCpmsAssignerPublisherRole), 'up')
#8 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(146): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\0.projects\\\\W...', 36, false)
#9 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(95): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#10 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(69): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#11 [internal function]: Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(29): call_user_func_array(Array, Array)
#13 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(87): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(31): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(549): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#17 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Command\\Command.php(255): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(170): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(992): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(255): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(148): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(88): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(121): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\0.projects\\WebApp\\acpms\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}
"} 
[2025-06-03 12:29:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:30:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:31:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:32:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:33:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:34:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:35:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
