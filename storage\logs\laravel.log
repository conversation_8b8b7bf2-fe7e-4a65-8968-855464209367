[2025-06-03 10:53:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:53:28] production.ERROR: Uncaught Error: Call to undefined method NPA\ACPMS\Http\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught Error: Call to undefined method NPA\\ACPMS\\Http\\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:53:44] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:54:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:55:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:56:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:57:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:58:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 10:59:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:00:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:01:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:02:27] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:03:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:04:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:05:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:06:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:07:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:08:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:09:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:10:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:11:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:12:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:13:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:14:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:15:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:16:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:17:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:18:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:19:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:20:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:21:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:22:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:23:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:24:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:25:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:26:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:27:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:28:36] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:29:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:30:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:31:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:32:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:33:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:34:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:35:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:36:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:37:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:38:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:39:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:40:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:41:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:42:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:43:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:44:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:45:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:46:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:47:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:48:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:49:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:50:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:51:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:52:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:53:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:54:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:55:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:56:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:57:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:58:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 11:59:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:00:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:01:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:02:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:03:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:04:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:05:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:06:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:07:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:08:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:09:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-03 12:10:28] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
