<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\Facades\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Filter;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Role;

class SpecialistChartController extends Controller
{
    public function index(Request $request)
    {
        $roleName = Role::find(auth('api')->user()->role_id)['name'];
        $specialistId = auth('api')->user()->id;
        $specialistIdCondition = ' ';
        if ($roleName === 'specialist') {
            $specialistIdCondition = ' and c.specialist_id = ' . $specialistId . ' ';
        } elseif ($roleName !== 'cpmd-manager' && $roleName !== 'cpmd-system-development' && $roleName !== 'assigner-publisher') {
            return response()->json([], 404);
        }

        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $queryString = Filter::prepareQueryStrings($request->query());
        $data['payments_percentage_chart'] = $this->paymentPercentageChart($specialistIdCondition, $queryString);
        $data['contracts_count_based_on_status_chart'] = $this->contractsCountBasedOnStatusChart($specialistIdCondition, $queryString);
        $data['contracts_value_based_on_status_chart'] = $this->contractsValueBasedOnStatusChart($specialistIdCondition, $queryString);
        $data['province_contracts_based_on_value'] = $this->provinceContractsBasedOnValue($specialistIdCondition, $queryString);
        $data['work_in_progress_contract_payments_volume_chart'] = $this->workInProgressContractPaymentsVolumeChart($specialistIdCondition, $queryString, $contractStatuses);
        $data['work_in_progress_contract_physical_progress_volume_chart'] = $this->workInProgressContractPhysicalProgressVolumeChart($specialistIdCondition, $queryString, $contractStatuses);
        $data['widgetsData'] = $this->widgets($specialistIdCondition, $queryString, $specialistId);
        $data['specialist_related_contracts'] = $this->contractsRelatedToSpecialist($specialistIdCondition, $queryString);
        $data['specialist_contracts_documents_upload'] = $this->specialistContractsDocumentsUpload($specialistIdCondition, $queryString, $procurementTypes);
        $data['specialist_contracts_documents_upload_consultancy_services'] = $this->specialistContractsDocumentsUploadConsultancyServices($specialistIdCondition, $queryString, $procurementTypes);
        $data['specialist_contract_status'] = $this->specialistContractStatus($specialistIdCondition, $queryString);
        $data['contract_price_percentage_procurement_entity'] = $this->contractPricePercentageProcurementEntity($specialistIdCondition, $queryString);

        $data['contracts_count_based_on_procurement_method'] = $this->contractsCountBasedOnProcurementMethod($specialistIdCondition, $queryString);
        $data['contract_value_based_on_donor'] = $this->contractValueBasedDonors($specialistIdCondition, $queryString);
        $data['contract_number_based_on_donor'] = $this->contractNumberBasedDonors($specialistIdCondition, $queryString);
        $data['analyzed_contracts_by_specialists'] = $this->analyzedContractsBySpecialists($queryString, $specialistId);
        $data['new_transferred_contracts_based_on_procurement_entity'] = $this->newTransferredContractsBasedOnProcurementEntity($specialistIdCondition, $queryString);
        $data['number_of_amendments_contracts'] = $this->numberOfAmendmentsContracts($specialistIdCondition, $queryString);
        $data['number_and_amount_of_contracts_by_company'] = $this->numberAndAmountOfContractsByCompany($specialistIdCondition, $queryString);
        $data['specialist_number_of_contracts_based_on_challenges'] = $this->specialistNumberOfContractsBasedOnChallenges($specialistIdCondition, $queryString);
        $data['contractChallengesContractAmount'] = $this->contractChallengesContractAmount($specialistIdCondition, $queryString);
        $data['companies'] = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company');

        return response()->json($data, 200);
    }

    private function paymentPercentageChart($specialistIdCondition, $queryString)
    {
        $data['plan_and_actual'] = DB::select('
                    select distinct
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                            sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        )   as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1 ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    
            ')[0];

        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                            if(
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    group by cr.iteration_count;   
            ');

        return $data;
    }

    private function contractsCountBasedOnStatusChart($specialistIdCondition, $queryString)
    {

        $data['contracts_count'] = DB::select
        ('
            select 
                count( distinct c.id) as contract_count
            from contracts as c 
             join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as csl_max
                  on csl_max.contract_id = c.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
	    ')[0];
        $data['status_based_contracts_count'] = DB::select
        ('
            select 
                cs.status_id as status_id,
                count(distinct cs.contract_id) as status_count
             from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c 
                on c.id = cs.contract_id
            ' . $queryString['joins'] . '
            where  1 = 1 ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
 
            
        ');

        return $data;
    }

    private function contractsValueBasedOnStatusChart($specialistIdCondition, $queryString)
    {
        $data['contracts_total_value'] = DB::select
        ('
           select round(sum(temp.contract_value)/ 1000000, 4) as contracts_total_value
                from	
                (select sum( (ifnull(cd.actual_value, 0) + ifnull((
                            select sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                            from amendments as a
                            left join cost_amendments as ca 
                                on ca.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            where a.contract_id =c.id
                                
                        ), 0) + ifnull(psc.provisional_sum_and_contingency, 0)) * if(c.exchange_rate, c.exchange_rate, 1)) as contract_value
                from contracts as c 
                join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
                  on cs.contract_id = c.id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
                ' . $queryString['joins'] . '
                where  1 = 1 ' . $specialistIdCondition . '
                ' . $queryString['query_string'] . '
                group by c.id, c.exchange_rate) as temp;
	    ')[0];
        $data['status_based_contracts_value'] = DB::select('
           
           select 
                cs.status_id as status_id,
                round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000, 4)  as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
            ) as amendment
                on c.id = amendment.contract_id 
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
        ');

        return $data;
    }

    private function provinceContractsBasedOnValue($specialistIdCondition, $queryString)
    {
        $data['no_shared_contracts'] = DB::select('
                select 
                    p.id as province_id, 
                    cs.status_id as contract_status_id, 
                    count(distinct(c.id)) as contracts_count,
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1) 
                    ) as total_contract_value
                from contracts as c 
                join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = c.id
                left join contract_details as cd 
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $queryString['joins'] . '
                where 1=1 ' . $queryString['query_string'] . '
                ' . $specialistIdCondition . '
                and c.id not in
                    (
                    select 
                        c.id as contract_id
                    from contracts as c 
                    join domestic_contract_execution_locations as dc 
                        on dc.contract_id = c.id 
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                    )
                group by p.id, cs.status_id;
        ');

        $data['shared_contracts'] = DB::select('
            select province_id, 
            sum(shared_contracts_count) as shared_contracts_count, 
            sum(total_contract_shared_value) as total_contract_shared_value 
            from (
                select 
                    provinces_contracts.province_id, 
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(join_c.exchange_rate, join_c.exchange_rate, 1) 
                    ) as total_contract_shared_value,
                    count(provinces_contracts.province_id) as shared_contracts_count
                from 
                    (
                        select distinct
                            p.id as province_id,
                            c.id as contract_id
                        from contracts as c
                        join domestic_contract_execution_locations as dcel
                            on dcel.contract_id = c.id
                        join temp_districts as d 
                            on d.id = dcel.district_id
                        join temp_provinces as p
                            on p.id = d.temp_province_id
                        
                    ) as provinces_contracts
                left join contract_details as cd
                    on cd.contract_id = provinces_contracts.contract_id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on provinces_contracts.contract_id = amendment.contract_id
                join contracts as join_c
                    on join_c.id = provinces_contracts.contract_id 
                where provinces_contracts.contract_id not in (
                    select 
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dcel
                        on dcel.contract_id = c.id
                    join temp_districts as d 
                        on d.id = dcel.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $queryString['query_string'] . '
                    ' . $specialistIdCondition . '
                    group by c.id
                    having count(distinct p.id) = 1
                )
                group by provinces_contracts.province_id, provinces_contracts.contract_id) as temp_table
            group by province_id;
        ');

        return $data;

    }

    private function workInProgressContractPaymentsVolumeChart($specialistIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];

        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select
            ('
                    select 
                        round(sum(ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4)  as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                        
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and  cpv.is_approved = true 
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 
                        ' . $specialistIdCondition . ' 
                        ' . $queryString['query_string'] . ' and 
                        cs.status_id = ' . $contract_status_id . ' and  
                        cp.year in (' . $value['year'] . ' ) and 
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $data['re_plan_and_actual'][$key] = DB::select
            ('
                    select 
                        cr.iteration_count as replan_number,
                        round(sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 
                        ' . $specialistIdCondition . ' 
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in (' . $value['year'] . ') and 
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }
        return $data;

    }

    private function workInProgressContractPhysicalProgressVolumeChart($specialistIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $tempPlan = 0;
        $tempActual = 0;

        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select
            (' select
                        round(
                        sum((cp.physical_progress_percentage  / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000, 4) + ' . $tempPlan . ' as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000 , 4) + ' . $tempActual . ' as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and cpv.is_approved = true


                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id


                    join contracts as c
                        on cp.contract_id = c.id
                    join contract_details as cd
	                    on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $queryString['joins'] . '
                    where 1 = 1  
                        ' . $specialistIdCondition . ' 
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        cp.year in  ( ' . $value['year'] . ' ) and
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $tempPlan = $data['plan_and_actual'][$key]->planning_progress_value ? $data['plan_and_actual'][$key]->planning_progress_value : 0;
            $tempActual = $data['plan_and_actual'][$key]->actual_progress_value ? $data['plan_and_actual'][$key]->actual_progress_value : 0;

            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        round(sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as actual_progress_value
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment
                        on c.id = amendment.contract_id
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $queryString['joins'] . '
                    where 1 = 1
                        ' . $specialistIdCondition . ' 
                        ' . $queryString['query_string'] . ' and
                         cs.status_id = ' . $contract_status_id . ' and
                        crp.year in  ( ' . $value['year'] . ' ) and
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }

        return $data;

    }

    private function widgets($specialistIdCondition, $queryString, $specialistId)
    {
        //inserted_contracts, contracts_published_to_website, contracts_analysis, contracts_not_analysis, contracts_analysis_by_managers, contracts_analysis_ready_for_publish_to_website
        //contracts_apply_to_change_information, uploadedDocumentPercentage DONE
        $data = DB::select('
        select
                ( 
                    select count(c.id) as inserted_contracts
                        from contracts as c 
                        ' . $queryString['joins'] . '
                        where 1 = 1  ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                ) as inserted_contracts
                ,(
                select 
                    count(max_value.max_id) as contracts_analysis
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                 ' . $queryString['joins'] . '
                where 1=1 ' . $specialistIdCondition . '
                   ' . $queryString['query_string'] . '
                ) as contracts_analysis
                ,(
                select 
                    (contracts_count-contracts_analysis) as 
                    contracts_not_analysis
                from(
                        select
                        (
                        select count(c.id) as contracts_count
                        from contracts as c
                         ' . $queryString['joins'] . '
                        where 1=1 ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                        ) as contracts_count
                        ,(
                        select 
                            count(max_value.max_id) as contracts_analysis
                        from contracts as c
                        join 
                            (
                                select 
                                    contract_id
                                    , max(id) as max_id 
                                from npa_other_comments
                                where is_confirmed = true 
                                group by contract_id
                            ) as max_value
                        on c.id = max_value.contract_id 
                         ' . $queryString['joins'] . '
                        where 1=1 ' . $specialistIdCondition . '
                           ' . $queryString['query_string'] . '
                        ) as contracts_analysis
                    ) as t
                ) as contracts_not_analysis
                ,(
                    select 
                    count(max_value.max_id) as contracts_analysis_by_managers
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_approved = true
                        and is_published = true
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                ) as contracts_analysis_by_managers
                ,( 
                select 
                count(max_value.max_id) as contracts_analysis_ready_for_publish_to_website
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true
                        and is_approved = false
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                ) as contracts_analysis_ready_for_publish_to_website
                ,(
                    select 
                        count(c.id) as contracts_published_to_website 
                        from contracts as c
                        ' . $queryString['joins'] . '
                        where c.is_published = true  ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                ) as contracts_published_to_website
                ,(
                    select 
                        count(c.id) as contracts_apply_to_change_information 
                        from contracts as c
                        ' . $queryString['joins'] . '
                        where c.has_requested_to_unpublish = true  ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                ) as contracts_apply_to_change_information');

        $temp = $this->uploadedDocumentPercentage($specialistIdCondition, $queryString);
        $data[0]->uploaded_document_percentage = $temp['uploaded']->count * 100 / ($temp['total']->contract_count ? $temp['total']->contract_count : 1);
        return $data[0];
    }

    private function contractsRelatedToSpecialist($specialistIdCondition, $queryString)
    {
        $data = DB:: select
        ('
                select 
                    (
                    select
                        count(c.id) as  assigned 
                        from contracts as c
                        ' . $queryString['joins'] . '
                    where 1 = 1  ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    ) as assigned
                    ,(
                    select 
                      count(c.id) as inserted
                    from contracts as c
                    join contract_general_information_verifications as cgiv
                    on cgiv.contract_id = c.id and 
                    cgiv.is_approved = true 
                     ' . $queryString['joins'] . '
                    where 1 = 1' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    ) as inserted
                    ,(
                    select 
                      count(c.id) as confirmed
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where c.is_confirmed = true ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    ) as confirmed
                    ,(
                    select
                        count(c.id) published 
                        from contracts as c
                        ' . $queryString['joins'] . '
                    where c.is_published = true and c.is_confirmed = true ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    ) as published
        ');

        return $data[0];

    }

    private function specialistContractsDocumentsUpload($specialistIdCondition, $queryString, $procurementTypes)
    {

        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $queryString['joins'] . '
                            where 1 = 1 ' . $specialistIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        
        
        ');

        return $data;
    }

    private function specialistContractsDocumentsUploadConsultancyServices($specialistIdCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyServices['id'] . '   ' . $specialistIdCondition . '
                 ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                        ' . $queryString['joins'] . '
                    where 1 = 1 ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'reference_terms\'
                 ,\'financial_tables\', \'action_plan\', \'negotiation_meeting_minutes\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'key_staffs\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name  = \'contract_closeout_report\'
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                        ' . $queryString['joins'] . '
                        where 1 = 1 ' . $specialistIdCondition . ' 
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $queryString['joins'] . '
                            where 1 = 1 ' . $specialistIdCondition . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');
        return $data;
    }

    public function procurement_type()
    {
        $path = 'api/dropDown/procurementType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function getProcurementTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->procurement_type(), $slug)['id'];
    }

    private function specialistContractStatus($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
          select 
                (
                    select
                        count(c.id) as  assigned_contracts_to_specialist 
                    from contracts as c
                     where 1 = 1  ' . $specialistIdCondition . '
                ) as assigned_contracts_to_specialist
                ,(
                 select 
                    count(max_value.max_id) as number_of_analysis_contracts
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                        ' . $queryString['joins'] . '
                         where 1=1 ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                 ) as number_of_analysis_contracts
                 ,(select 
                    count(max_value.max_id) as number_of_analysis_contracts
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true
                        and is_published = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                         ' . $queryString['joins'] . '
                where 1 = 1 ' . $specialistIdCondition . '
                ' . $queryString['query_string'] . '
              ) as specialist_contracts_published_to_website
        ');
        return $data[0];
    }

    private function contractPricePercentageProcurementEntity($specialistIdCondition, $queryString)
    {
        $data['grand_total_cost_of_contracts'] = DB::select
        ('
                   select	
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as grand_total_cost
                  from contracts as c 
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
              ' . $queryString['joins'] . '
            where 1 = 1 ' . $specialistIdCondition . '
              ' . $queryString['query_string'] . '
        ');

        $data['total_cost_per_procurement_entity'] = DB::select
        ('
             select	
                   sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as  total_cost, 
                  c.procurement_entity_id
                  from contracts as c 
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                  ' . $queryString['joins'] . '
                  where 1 = 1 ' . $specialistIdCondition . '
                  ' . $queryString['query_string'] . '
                 group by c.procurement_entity_id
        ');
        for ($i = 0; $i < sizeof($data['total_cost_per_procurement_entity']); $i++) {
            $data['total_cost_per_procurement_entity'][$i]->percentage =
                $data['grand_total_cost_of_contracts'][0]->grand_total_cost == 0 ? 0 :
                    round(
                        ($data['total_cost_per_procurement_entity'][$i]->total_cost * 100) /
                        $data['grand_total_cost_of_contracts'][0]->grand_total_cost, 2
                    );
        }
        return $data;
    }

    private function contractsCountBasedOnProcurementMethod($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
            select
              count(c.id) as contrats_count,
              c.procurement_method_id
            from contracts as c
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
            group by c.procurement_method_id
        ');
        return $data;
    }

    private function contractValueBasedDonors($specialistIdCondition, $queryString)
    {
        $data = DB::select('
                    	  select	
                          round(sum((cd.actual_value + ifnull(psc.provisional_sum_and_contingency,0)
                    + ifnull(amendment.amount,0)
                )* if(c.exchange_rate, c.exchange_rate, 1)) / 1000000, 4) as total_contracts_value,cdo.donor_id as donor_id    
                              from contracts as c 
                               left join (
                                 select 
                                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                        a.contract_id as contract_id
                                    from amendments as a
                                    left join cost_amendments as ca 
                                        on ca.amendment_id = a.id
                                    left join time_and_cost_amendments as tca
                                        on tca.amendment_id = a.id
                                    group by  a.contract_id 
                            
                                ) as amendment
                                    on c.id = amendment.contract_id
                              inner join contract_donors as cdo
                                on c.id = cdo.contract_id
                              join contract_details as cd
                                  on c.id = cd.contract_id
                              join contract_details_verifications cdv
                                  on cd.id = cdv.contract_detail_id 
                                  and cdv.is_approved = true
                           left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                              ' . $queryString['joins'] . '
                              where 1 = 1 ' . $specialistIdCondition . '
                              ' . $queryString['query_string'] . '
                              group by cdo.donor_id;
                    ');
        return $data;
    }

    private function contractNumberBasedDonors($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
              select
                  count(c.id) as number_of_contracts,
                  cd.donor_id
              from contracts as c
              join contract_donors as cd
                  on c.id = cd.contract_id
              ' . $queryString['joins'] . '
              where 1 = 1 ' . $specialistIdCondition . '
              ' . $queryString['query_string'] . '
              group by cd.donor_id;
             ');
        return $data;
    }

    private function analyzedContractsBySpecialists($queryString, $specialistId)
    {

        $condition = ' ';
        if ($specialistId) {
            $sector_ids = '';
            $return_sector_id = DB::select
            ('select
                us.sector_id, 
                u.role_id as role_id
              from user_specialists as us
              join users as u
                on u.id = us.user_id
              where us.user_id = ' . $specialistId . '
                         ');
            for ($i = 0; $i < sizeof($return_sector_id); $i++) {
                $sector_ids .= $return_sector_id[$i]->sector_id . ',';
                $specialistRoleId = $return_sector_id[$i]->role_id;
            }

            $sector_id = rtrim($sector_ids, ',');
            $condition = $sector_id !== '' ? ' and c.sector_id in (' . $sector_id . ') ' : '';
        }

        $data = DB::select('
                    select
                           (select full_name from users where id = u.id) as full_name,
                           count(case when noc.is_confirmed = true then 1 end) as contracts_count
                        from users as u
                        join contracts as c
                          on c.specialist_id = u.id
                        join user_specialists as us
                          on us.user_id = u.id	
                        left join npa_other_comments as noc
                           on c.id = noc.contract_id
                        ' . $queryString['joins'] . '
                        where  1 = 1
                        ' . $queryString['query_string'] . '
                        ' . $condition . '
                        group by u.id');

        return $data;
    }

    private function newTransferredContractsBasedOnProcurementEntity($specialistIdCondition, $queryString)
    {

        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $year = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $year = $current_jalali_date[0] + 1;
        }


        $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
        $fiscalEndArray = JDateTime::toGregorian($year, 9, 30);
        $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;
        $fiscalEndDate = $fiscalEndArray ? $fiscalEndArray[0] . '-' . $fiscalEndArray[1] . '-' . $fiscalEndArray[2] : null;

        $statuses_id = DropDowns::getBySlugs(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            ['contract-close-out', 'cancelled']);
        $idQuery = '';
        foreach ($statuses_id as $item) {
            $idQuery .= $item['id'] . ',';
        }
        $idQuery = substr($idQuery, 0, -1);

        $data = DB::select
        ('
             select
                count(case when cp.actual_start_date < "' . $fiscalStartDate . '"  and ( cs.status_id not in (' . $idQuery . ') or cs.status_id is null) then 1 end) as transferred_contracts_count,
                count(case when cp.actual_start_date between "' . $fiscalStartDate . '" and "' . $fiscalEndDate . '" then 1 end) as new_contracts_count,
                c.procurement_entity_id
            from contracts as c
            inner join contract_progresses as cp
                on c.id = cp.contract_id
            join (
                select 
                    csl.id, csl.contract_id, csl.status_id 
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                    on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = cp.contract_id
            ' . $queryString['joins'] . '
            where 1 = 1 
            ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
            group by c.procurement_entity_id       
        ');
        return $data;
    }

    private function numberOfAmendmentsContracts($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
                    select
                        count(c.id) as number_of_contracts,
                        a.type_id
                    from contracts as c
                    join amendments as a
                        on c.id = a.contract_id
                    ' . $queryString['joins'] . '
                    where a.is_approved = true
                    ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                    group by a.type_id
                ');
        return $data;
    }

    private function numberAndAmountOfContractsByCompany($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
            
            select
                com.company_id,
                count(c.id) as contract_count,
                round(sum(
                        ((ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)) * ifnull(com.percentage_contract_share, 100) / 100
                    ) / 1000000 , 4) as total_contracts_amount
                
            from companies as com
            join company_general_informations as cgi
                on cgi.id = com.company_general_information_id
            join contracts as c
                on cgi.contract_id = c.id
            left join contract_details as cd
                on c.id = cd.contract_id
            left join contract_details_verifications cdv
                on cd.id = cdv.contract_detail_id
                and cdv.is_approved = true
            left join (
                 select 
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca 
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    group by  a.contract_id 
            
               ) as amendment
                      on c.id = amendment.contract_id
                      
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $specialistIdCondition . '
            ' . $queryString['query_string'] . '
            group by com.company_id            
        ');
        return $data;
    }

    private function specialistNumberOfContractsBasedOnChallenges($specialistIdCondition, $queryString)
    {
        $data = DB::select
        ('
            select
                    total_contracts_count,
                    challenges_contracts_count,
                    (total_contracts_count - challenges_contracts_count) as not_challenge_contracts_count
                from 
                (
                    select
                        count(distinct (c.id)) as total_contracts_count,
                        count(distinct (cr.contract_id)) as challenges_contracts_count
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join challenges_and_remarks as cr
                    on c.id = cr.contract_id
                    and cr.is_approved = true
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                ' . $queryString['joins'] . '
                where 1 = 1 ' . $specialistIdCondition . '
                ' . $queryString['query_string'] . '
                ) as t
        ');
        return $data[0];
    }

    public function attachmentCount()
    {
        $path = 'api/attachmentAmount?module_slug=cpms';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    private function uploadedDocumentPercentage($specialistIdCondition, $queryString)
    {
        $data['total'] = DB::select('
                select 
                    count(c.id ) * 15 as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $specialistIdCondition . '
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                count( att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                     ' . $queryString['joins'] . '
                    where  1 = 1   ' . $specialistIdCondition . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1  ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $specialistIdCondition . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                             ' . $queryString['joins'] . '
                            where  1 = 1   ' . $specialistIdCondition . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
        ')[0];
        return $data;

    }

    private function contractChallengesContractAmount($specialistIdCondition, $queryString)
    {
        $data = DB::select('
                     select 
                    car_distinct.category_id,
                    count(car_distinct.contract_id) contracts_count,
                    max(car_distinct.contract_id),
                    round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amount
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                   ) as amendment
                          on c.id = amendment.contract_id
               left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
               join (
                    select distinct 
                      inner_car.category_id,
                      inner_car.contract_id
                    from challenges_and_remarks as inner_car
                    where inner_car.is_approved = true
                    group by inner_car.category_id, inner_car.contract_id, inner_car.id
                 )as car_distinct 
                    on car_distinct.contract_id = c.id
                 ' . $queryString['joins'] . '
               where 1 = 1    ' . $specialistIdCondition . '
               ' . $queryString['query_string'] . ' 
               group by car_distinct.category_id'
        );
        return $data;
    }
}
