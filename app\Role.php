<?php

namespace NPA\ACPMS;

use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    protected $guarded = ['id'];

    protected $fillable = [
        'name',
        'da_name',
        'can_be_acted',
        'can_act'
    ];

    protected $casts = [
        'can_be_acted' => 'boolean',
        'can_act' => 'boolean'
    ];

    public function contexts()
    {
        return $this->hasMany('NPA\ACPMS\Models\Context');
    }
}
