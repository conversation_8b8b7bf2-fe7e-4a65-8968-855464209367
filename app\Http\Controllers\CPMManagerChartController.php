<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Filter;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\UserCpmManager;
use NPA\ACPMS\Role;

class CPMManagerChartController extends Controller
{

    public function index(Request $request)
    {
        $sectorIds = '';
        $sectorIdsArray = [];
        $roleName = Role::find(auth('api')->user()->role_id)['name'];
        $userCpmManager = UserCpmManager::select('sector_id')->where('user_id', auth('api')->user()->id)->get();
        foreach ($userCpmManager as $value) {
            $sectorIds .= $value['sector_id'] . ',';
            array_push($sectorIdsArray, $value['sector_id']);
        }
        $sectorIds = rtrim($sectorIds, ',');
        $sectorIdCondition = ' ';
        if ($roleName === 'cpmd-manager') {
            $sectorIdCondition = ' and c.sector_id in (' . $sectorIds . ') ';
        } elseif ($roleName !== 'cpmd-manager' && $roleName !== 'cpmd-system-development' && $roleName !== 'assigner-publisher') {
            return response()->json([], 404);
        }
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $queryString = Filter::prepareQueryStrings($request->query());
        $data['payments_percentage_chart'] = $this->paymentPercentageChart($sectorIdCondition, $queryString, $contractStatuses);
        $data['contracts_count_based_on_status_chart'] = $this->contractsCountBasedOnStatusChart($sectorIdCondition, $queryString);
        $data['contracts_value_based_on_status_chart'] = $this->contractsValueBasedOnStatusChart($sectorIdCondition, $queryString);
        $data['cpm_manager_number_of_contracts_based_On_challenges'] = $this->cpmManagerNumberOfContractsBasedOnChallenges($sectorIdCondition, $queryString);
        $data['province_contracts_based_on_value'] = $this->provinceContractsBasedOnValue($sectorIdCondition, $queryString);
        $data['work_in_progress_contract_payments_volume_chart'] = $this->workInProgressContractPaymentsVolumeChart($sectorIdCondition, $queryString, $contractStatuses);
        $data['work_in_progress_contract_physical_progress_volume_chart'] = $this->workInProgressContractPhysicalProgressVolumeChart($sectorIdCondition, $queryString, $contractStatuses);
        $data['widgetsData'] = $this->widgets($sectorIdCondition, $queryString);
        $data['cpmManagerContractStatus'] = $this->cpmManagerContractStatus($sectorIdCondition, $queryString);
        $data['cpmManagerUploadedDocuments'] = $this->cpmManagerDocumentsUploaded($sectorIdCondition, $queryString, $procurementTypes);
        $data['cpmManagerUploadedDocumentsConsultancy'] = $this->cpmManagerDocumentsUploadedConsultancy($sectorIdCondition, $queryString, $procurementTypes);
        $data['cpmManagerProcurementMethod'] = $this->cpmManagerProcurementMethodContracts($sectorIdCondition, $queryString);
        $data['contract_price_percentage_procurement_entity'] = $this->contractPricePercentageProcurementEntity($sectorIdCondition, $queryString);
        $data['cpmManagerContractPercentageProcurementEntity'] = $this->cpmManagerContractPercentageBasedOnProcurementEntity($sectorIdCondition, $queryString, $sectorIdsArray);
        $data['cpmManagerContractAmountBasedOnDonor'] = $this->cpmManagerContractAmountBasedOnDonor($sectorIdCondition, $queryString);
        $data['cpmManagerContractNumberBasedOnDonor'] = $this->cpmManagerContractNumberBasedOnDonor($sectorIdCondition, $queryString);
        $data['cpmManagerAnalyzedContractBySpecialist'] = $this->cpmManagerAnalyzedContractBySpecialist($sectorIdCondition, $queryString);
        $data['cpmManagerNumberOfAmendmentContracts'] = $this->cpmManagerNumberOfAmendmentContracts($sectorIdCondition, $queryString);
        $data['new_transferred_contracts_based_on_procurement_entity'] = $this->newTransferredContractsBasedOnProcurementEntity($sectorIdCondition, $queryString);
        $data['cpmManagerNumberAndAmountOfContractsBasedOnCompany'] = $this->cpmManagerNumberAndAmountOfContractsBasedOnCompany($sectorIdCondition, $queryString);
        $data['contractChallengesContractAmount'] = $this->contractChallengesContractAmount($sectorIdCondition, $queryString);
        $data['companies'] = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company');
        return response()->json($data);
    }

    private function paymentPercentageChart($sectorIdCondition, $queryString, $contractStatuses)
    {
        $contractCompleteStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');
        $contract_status_id = $contractCompleteStatus['id'];
        $data['plan_and_actual'] = DB::select('
                    select distinct
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                            sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        )   as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                     join (
                            select 
                                csl.id, csl.contract_id, csl.status_id 
                            from contract_status_logs as csl
                            join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                            on csl.id = max_value.max_id
                        ) as csl_max
                          on csl_max.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1 ' . $sectorIdCondition . '
                    ' . $queryString['query_string'] . '
                    and csl_max.status_id = ' . $contract_status_id . '
                    
            ')[0];

        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                            if(
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                     join (
                            select 
                                csl.id, csl.contract_id, csl.status_id 
                            from contract_status_logs as csl
                            join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                            on csl.id = max_value.max_id
                        ) as csl_max
                          on csl_max.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $sectorIdCondition . '
                    ' . $queryString['query_string'] . '
                    and csl_max.status_id = ' . $contract_status_id . '
                    group by cr.iteration_count;   
            ');

        return $data;
    }

    private function contractsCountBasedOnStatusChart($sectorIdCondition, $queryString)
    {
        $data['contracts_count'] = DB::select('
            select 
                count(distinct c.id) as contract_count
            from contracts as c 
            join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( select contract_id, max(id) as max_id from contract_status_logs 
                    group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as csl_max
                    on csl_max.contract_id = c.id
            ' . $queryString['joins'] . '
            where  1 = 1  ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . ';
	    ')[0];
        $data['status_based_contracts_count'] = DB::select('
            select 
                cs.status_id as status_id,
                count( distinct cs.id) as status_count
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( select contract_id, max(id) as max_id FROM contract_status_logs 
                    group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c 
                on c.id = cs.contract_id
            ' . $queryString['joins'] . '
            where 1 = 1  ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
        ');
        return $data;
    }

    private function contractsValueBasedOnStatusChart($sectorIdCondition, $queryString)
    {
        $data['contracts_total_value'] = DB::select('
            select round(sum(temp.contract_value)/ 1000000, 4) as contracts_total_value
                from	
                (select sum( (ifnull(cd.actual_value, 0) + ifnull((
                            select sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                            from amendments as a
                            left join cost_amendments as ca 
                                on ca.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            where a.contract_id =c.id
                                
                        ), 0) + ifnull(psc.provisional_sum_and_contingency, 0)) * if(c.exchange_rate, c.exchange_rate, 1)) as contract_value
                from contracts as c 
                join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
                  on cs.contract_id = c.id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $sectorIdCondition . '
                ' . $queryString['query_string'] . '
                group by c.id, c.exchange_rate) as temp;
	    ')[0];

        $data['status_based_contracts_value'] = DB::select('
             select 
                cs.status_id as status_id,
                round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000, 4)  as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
            ) as amendment
                on c.id = amendment.contract_id 
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
        ');
        return $data;
    }

    private function cpmManagerNumberOfContractsBasedOnChallenges($sectorIdCondition, $queryString)
    {
        $data = DB::select('
            select 
                total_contracts_count, 
                challenges_contracts_count,
                (total_contracts_count-challenges_contracts_count) as not_challenge_contracts_count
            from 
            (
                select 
                    count(distinct(c.id)) as total_contracts_count, 
                    count(distinct (cr.contract_id)) as challenges_contracts_count
                from contracts as c
                    left Join challenges_and_remarks as cr 
                    on c.id = cr.contract_id and cr.is_approved = true
                 ' . $queryString['joins'] . '
                where 1 = 1  ' . $sectorIdCondition . '
                ' . $queryString['query_string'] . '
            ) as t
                ');
        return $data[0];
    }

    private function provinceContractsBasedOnValue($sectorIdCondition, $queryString)
    {
        $data['no_shared_contracts'] = DB::select('
                select 
                    p.id as province_id, 
                    cs.status_id as contract_status_id, 
                    count(distinct(c.id)) as contracts_count,
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1) 
                    ) as total_contract_value
                from contracts as c 
                join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = c.id
                left join contract_details as cd 
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $queryString['joins'] . '
                where 1=1 ' . $queryString['query_string'] . '
                ' . $sectorIdCondition . '
                and c.id not in
                    (
                    select 
                        c.id as contract_id
                    from contracts as c 
                    join domestic_contract_execution_locations as dc 
                        on dc.contract_id = c.id 
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                    )
                group by p.id, cs.status_id;
        ');

        $data['shared_contracts'] = DB::select('
            select province_id, 
            sum(shared_contracts_count) as shared_contracts_count, 
            sum(total_contract_shared_value) as total_contract_shared_value 
            from (
                select 
                    provinces_contracts.province_id, 
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(join_c.exchange_rate, join_c.exchange_rate, 1) 
                    ) as total_contract_shared_value,
                    count(provinces_contracts.province_id) as shared_contracts_count
                from 
                    (
                        select distinct
                            p.id as province_id,
                            c.id as contract_id
                        from contracts as c
                        join domestic_contract_execution_locations as dcel
                            on dcel.contract_id = c.id
                        join temp_districts as d 
                            on d.id = dcel.district_id
                        join temp_provinces as p
                            on p.id = d.temp_province_id
                        
                    ) as provinces_contracts
                left join contract_details as cd
                    on cd.contract_id = provinces_contracts.contract_id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on provinces_contracts.contract_id = amendment.contract_id
                join contracts as join_c
                    on join_c.id = provinces_contracts.contract_id 
                where provinces_contracts.contract_id not in (
                    select 
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dcel
                        on dcel.contract_id = c.id
                    join temp_districts as d 
                        on d.id = dcel.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $queryString['query_string'] . '
                    ' . $sectorIdCondition . '
                    group by c.id
                    having count(distinct p.id) = 1
                )
                group by provinces_contracts.province_id, provinces_contracts.contract_id) as temp_table
            group by province_id;
        ');

        return $data;

    }

    private function workInProgressContractPaymentsVolumeChart($sectorIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];

        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                     select 
                        round(sum(ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4)  as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                        
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and  cpv.is_approved = true 
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1
                        ' . $sectorIdCondition . ' 
                         ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and  
                        cp.year in  ( ' . $value['year'] . ' ) and 
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $data['re_plan_and_actual'][$key] = DB::select('
                    select 
                        cr.iteration_count as replan_number,
                        round(sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $sectorIdCondition . ' 
                         ' . $queryString['query_string'] . ' and 
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in (' . $value['year'] . ') and 
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }

        return $data;

    }

    private function workInProgressContractPhysicalProgressVolumeChart($sectorIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $tempPlan = 0;
        $tempActual = 0;
        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select
                        round(
                        sum((cp.physical_progress_percentage  / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000, 4) + ' . $tempPlan . ' as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000 , 4) + ' . $tempActual . ' as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and cpv.is_approved = true


                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id


                    join contracts as c
                        on cp.contract_id = c.id
                    join contract_details as cd
	                    on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        cp.year in  ( ' . $value['year'] . ' ) and
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $tempPlan = $data['plan_and_actual'][$key]->planning_progress_value ? $data['plan_and_actual'][$key]->planning_progress_value : 0;
            $tempActual = $data['plan_and_actual'][$key]->actual_progress_value ? $data['plan_and_actual'][$key]->actual_progress_value : 0;


            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        round(sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as actual_progress_value
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment
                        on c.id = amendment.contract_id
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                        ' . $queryString['joins'] . '
                    where 1 = 1 ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in  ( ' . $value['year'] . ' ) and
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;

        ');

        }

        return $data;
    }

    private function widgets($sectorIdCondition, $queryString)
    {
        //inserted_contracts, contracts_published_to_website, notanalyzed done, contracts_analysis_by_managers, contracts_analysis_ready_to_published_to_website
        //contracts_to_change_information, uploadedDocumentPercentage DONE
        $data = DB::select('
        select 
            (
            select 
              ifnull(count(distinct c.id),0) as saved_contracts_by_sector 
            from contracts as c
              ' . $queryString['joins'] . '
            where 1 = 1 
              ' . $sectorIdCondition . '
              ' . $queryString['query_string'] . '
            ) as inserted_contracts
            ,(	
                select 
                    count(max_value.max_id) as contracts_analysis
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                 ' . $queryString['joins'] . '
                where 1=1 ' . $sectorIdCondition . '
                   ' . $queryString['query_string'] . '
            ) as analyzed
            ,(
                select 
                    (contracts_count-contracts_analysis) as 
                    contracts_not_analysis
                from(
                        select
                        (
                        select count(c.id) as contracts_count
                        from contracts as c
                         ' . $queryString['joins'] . '
                        where 1=1 ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . '
                        ) as contracts_count
                        ,(
                        select 
                            count(max_value.max_id) as contracts_analysis
                        from contracts as c
                        join 
                            (
                                select 
                                    contract_id
                                    , max(id) as max_id 
                                from npa_other_comments
                                where is_confirmed = true 
                                group by contract_id
                            ) as max_value
                        on c.id = max_value.contract_id 
                         ' . $queryString['joins'] . '
                        where 1=1 ' . $sectorIdCondition . '
                           ' . $queryString['query_string'] . '
                        ) as contracts_analysis
                    ) as t
            ) as notanalyzed
            ,(
                select 
                    count(distinct c.id) as contracts_analysis_by_managers 
                    from npa_other_comments
                    left join contracts as c
                        on c.id = npa_other_comments.contract_id
                    ' . $queryString['joins'] . '
                    where  npa_other_comments.is_published = true 
                    ' . $sectorIdCondition . ' 
                    ' . $queryString['query_string'] . '
            ) as contracts_analysis_by_managers
            ,(
                select 
                    count(distinct c.id) as contracts_analysis_ready_to_published_to_website 
                    from npa_other_comments
                    left join contracts as c 
                        on c.id = npa_other_comments.contract_id
                    ' . $queryString['joins'] . '
                    where  npa_other_comments.is_confirmed = true and npa_other_comments.is_approved = false
                    ' . $sectorIdCondition . ' 
                    ' . $queryString['query_string'] . '
            ) as contracts_analysis_ready_to_published_to_website
            ,(
                select 
                    count(distinct c.id) as contracts_published_to_website 
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where  c.is_published = true 
                    ' . $sectorIdCondition . ' 
                    ' . $queryString['query_string'] . '
            ) as contracts_published_to_website	
            ,(
                select 
                    count(distinct c.id) as contracts_to_change_information 
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where  c.has_requested_to_unpublish = true 
                    ' . $sectorIdCondition . '
                    ' . $queryString['query_string'] . ' 
            ) as contracts_to_change_information
            ');
        $temp = $this->uploadedDocumentPercentage($sectorIdCondition, $queryString);
        $data[0]->uploaded_document_percentage = $temp['uploaded']->count * 100 / $temp['total']->contract_count;
        return $data[0];
    }


    private function uploadedDocumentPercentage($sectorIdCondition, $queryString)
    {
        $data['total'] = DB::select('
                select 
                    count(c.id ) * 15 as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $sectorIdCondition . '
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                count( att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                     ' . $queryString['joins'] . '
                    where  1 = 1   ' . $sectorIdCondition . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1  ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                             ' . $queryString['joins'] . '
                            where  1 = 1   ' . $sectorIdCondition . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
        ')[0];
        return $data;

    }


    private function cpmManagerContractStatus($sectorIdCondition, $queryString)
    {
        $published_contracts_by_sector_to_website = DB::select('select 
                                                                  ifnull(count(distinct c.id),0) as published_contracts_by_sector_to_website 
                                                                from contracts as c
                                                                ' . $queryString['joins'] . '
                                                                where  c.is_published = true 
                                                                ' . $sectorIdCondition . '
                                                                ' . $queryString['query_string'] . ' ');
        $data['published_contracts_by_sector_to_website'] = $published_contracts_by_sector_to_website[0]->published_contracts_by_sector_to_website;

        $approved_contracts_by_contract_manager = DB::select('select 
                                                                ifnull(count(distinct c.id),0) as approved_contracts_by_contract_manager 
                                                              from contracts as c
                                                              ' . $queryString['joins'] . '
                                                              where  c.is_confirmed = true 
                                                              ' . $sectorIdCondition . '
                                                              ' . $queryString['query_string'] . ' ');
        $data['approved_contracts_by_contract_manager'] = $approved_contracts_by_contract_manager[0]->approved_contracts_by_contract_manager;

        $saved_contracts = DB::select('select 
                                          ifnull(count(distinct c.id),0) as saved_contracts_by_sector 
                                        from contracts as c
                                          ' . $queryString['joins'] . '
                                        where 1 = 1 
                                          ' . $sectorIdCondition . '
                                          ' . $queryString['query_string'] . ' ');
        $data['saved_contracts_by_sector'] = $saved_contracts[0]->saved_contracts_by_sector;

        $sector_contract_amount = DB::select('select 
                                                    ifnull(count(distinct c.id),0) as sector_contract_amount 
                                                from contracts as c
                                                ' . $queryString['joins'] . '
                                                where 1 = 1  ' . $sectorIdCondition . '
                                                ' . $queryString['query_string'] . ' ');
        $data['sector_contract_amount'] = $sector_contract_amount[0]->sector_contract_amount;
        return $data;
    }

    private function cpmManagerDocumentsUploaded($sectorIdCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                     ' . $queryString['joins'] . '
                    where  1 = 1   ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1  ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                             ' . $queryString['joins'] . '
                            where  1 = 1   ' . $sectorIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');

        return $data;
    }

    private function cpmManagerDocumentsUploadedConsultancy($sectorIdCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');

        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyServices['id'] . '   ' . $sectorIdCondition . '
                 ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                        ' . $queryString['joins'] . '
                    where  1 = 1   ' . $sectorIdCondition . '
                     ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'reference_terms\'
                 ,\'financial_tables\', \'action_plan\', \'negotiation_meeting_minutes\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'key_staffs\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . '
                         ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . '
                         ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name  = \'contract_closeout_report\'
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $sectorIdCondition . '
                         ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $queryString['joins'] . '
                            where  1 = 1   ' . $sectorIdCondition . '
                             ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');
        return $data;
    }

    public function procurement_type()
    {
        $path = 'api/dropDown/procurementType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function getProcurementTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->procurement_type(), $slug)['id'];
    }

    public function attachmentCount()
    {
        $path = 'api/attachmentAmount?module_slug=cpms';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    private function cpmManagerProcurementMethodContracts($sectorIdCondition, $queryString)
    {
        $data['procurement_entity'] = $this->procurement_entity();

        $query = DB::select('
                    select
                        c.procurement_entity_id,
                        c.procurement_method_id,
                        count(c.id) as contracts_count
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $sectorIdCondition . '
                    ' . $queryString['query_string'] . '
                    group by c.procurement_entity_id , c.procurement_method_id
                
        ');

        $data['procurement_method_calculations'] = $query;

        return $data;
    }

    public function procurement_entity()
    {
        $sectors = DropDowns::getWithCondition(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL')
            . 'api/specific/sectorAndProcurementEntity/procurementEntity', 'sector_id', 3);
        return $sectors;
    }

    private function contractPricePercentageProcurementEntity($sectorIdCondition, $queryString)
    {
        $data['grand_total_cost_of_contracts'] = DB::select
        ('
                   select	
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as grand_total_cost
                  from contracts as c 
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
              ' . $queryString['joins'] . '
            where 1 = 1 ' . $sectorIdCondition . '
              ' . $queryString['query_string'] . '
        ');

        $data['total_cost_per_procurement_entity'] = DB::select
        ('
             select	
                   sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as  total_cost, 
                  c.procurement_entity_id
                  from contracts as c 
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                  ' . $queryString['joins'] . '
                  where 1 = 1 ' . $sectorIdCondition . '
                  ' . $queryString['query_string'] . '
                 group by c.procurement_entity_id
        ');
        for ($i = 0; $i < sizeof($data['total_cost_per_procurement_entity']); $i++) {
            $data['total_cost_per_procurement_entity'][$i]->percentage =
                $data['grand_total_cost_of_contracts'][0]->grand_total_cost == 0 ? 0 :
                    round(
                        ($data['total_cost_per_procurement_entity'][$i]->total_cost * 100) /
                        $data['grand_total_cost_of_contracts'][0]->grand_total_cost, 2
                    );
        }
        return $data;
    }

    private function cpmManagerContractPercentageBasedOnProcurementEntity($sectorIdCondition, $queryString, $sectorIds)
    {
        $data['sector_ids'] = $sectorIds;
        $data['grand_total_cost_of_contracts'] = DB::select('
                select	
                  sum((cd.actual_value + ifnull(psc.provisional_sum_and_contingency,0)
                    + ifnull((
                    select 
                            sum(
                                ifnull(ca.amendment_amount, 0)+ ifnull(tca.amendment_amount, 0)) as sumofAll
                        from  amendments as a
                          left join cost_amendments as ca 
                              on a.id = ca.amendment_id
                          left join time_and_cost_amendments as tca
                              on a.id = tca.amendment_id
                        where c.id = a.contract_id and a.is_approved = true
                    ),0)
                )* if(c.exchange_rate, c.exchange_rate, 1)) as grand_total_cost
                  from contracts as c 
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                                  ' . $queryString['joins'] . '
                                  where 1 = 1  ' . $sectorIdCondition . '
                                  ' . $queryString['query_string'] . '
                                                     ');

        $data['total_cost_per_procurement_entity'] = DB::select('
                              select	
                                  sum((cd.actual_value * ifnull(c.exchange_rate, 1)) + 
                                    ( select 
                                ifnull(
                                    sum(
                                        ifnull(ca.amendment_amount, 0) * 
                                        ifnull(c.exchange_rate, 1) + ifnull(tca.amendment_amount, 0)* 
                                        ifnull(c.exchange_rate, 1)), 0) as sumofAll
                                from  amendments as a
                                  left join cost_amendments as ca 
                                      on a.id = ca.amendment_id
                                  left join time_and_cost_amendments as tca
                                      on a.id = tca.amendment_id
                                where c.id = a.contract_id and a.is_approved = true)
                            
                                ) as total_cost, c.procurement_entity_id
                                  from contracts as c 
                                  join contract_details as cd
                                      on c.id = cd.contract_id
                                  join contract_details_verifications cdv
                                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                                   ' . $queryString['joins'] . '
                                      where 1 = 1 ' . $sectorIdCondition . '
                                      ' . $queryString['query_string'] . '
                                 group by c.procurement_entity_id');
        for ($i = 0; $i < sizeof($data['total_cost_per_procurement_entity']); $i++) {
            $data['total_cost_per_procurement_entity'][$i]->percentage =
                $data['grand_total_cost_of_contracts'][0]->grand_total_cost == 0 ? 0 :
                    round(
                        ($data['total_cost_per_procurement_entity'][$i]->total_cost * 100) /
                        $data['grand_total_cost_of_contracts'][0]->grand_total_cost, 2
                    );
        }
        return $data;
    }

    private function cpmManagerContractAmountBasedOnDonor($sectorIdCondition, $queryString)
    {
        $data = DB::select('
                    	  select	
                          round(sum((cd.actual_value + ifnull(psc.provisional_sum_and_contingency,0)
                    + ifnull(amendment.amount,0)
                )* if(c.exchange_rate, c.exchange_rate, 1)) / 1000000, 4) as total_contracts_value,cdo.donor_id as donor_id    
                              from contracts as c 
                               left join (
                                 select 
                                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                        a.contract_id as contract_id
                                    from amendments as a
                                    left join cost_amendments as ca 
                                        on ca.amendment_id = a.id
                                    left join time_and_cost_amendments as tca
                                        on tca.amendment_id = a.id
                                    group by  a.contract_id 
                            
                                ) as amendment
                                    on c.id = amendment.contract_id
                              inner join contract_donors as cdo
                                on c.id = cdo.contract_id
                              join contract_details as cd
                                  on c.id = cd.contract_id
                              join contract_details_verifications cdv
                                  on cd.id = cdv.contract_detail_id 
                                  and cdv.is_approved = true
                           left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                              ' . $queryString['joins'] . '
                              where 1 = 1 ' . $sectorIdCondition . '
                              ' . $queryString['query_string'] . '
                              group by cdo.donor_id;
                    ');
        return $data;
    }

    private function cpmManagerContractNumberBasedOnDonor($sectorIdCondition, $queryString)
    {
        $data = DB::select("select
                count(c.id) as number_of_contracts,
                cd.donor_id
            from contracts as c
            join contract_donors as cd
                on c.id = cd.contract_id
            " . $queryString['joins'] . "
            where 1 = 1  " . $sectorIdCondition . " 
            " . $queryString['query_string'] . "
            group by cd.donor_id;");
        return $data;
    }

    private function cpmManagerAnalyzedContractBySpecialist($sectorIdCondition, $queryString)
    {

        $data = DB::select('
                    select
                           (select full_name from users where id = u.id) as full_name,
                              round(ifnull(count(case when noc.is_confirmed = true then 1 end), 0) * 
                              100 / 
                              if(count(distinct c.id) , count(distinct c.id) , 1), 2) as total_contract_percentage
                           
                        from users as u
                        join contracts as c
                          on c.specialist_id = u.id
                        join user_specialists as us
                          on us.user_id = u.id	
                        left join npa_other_comments as noc
                           on c.id = noc.contract_id
                        ' . $queryString['joins'] . '
                        where  1 = 1
                        ' . $queryString['query_string'] . '
                        ' . $sectorIdCondition . '
                        group by u.id');
        return $data;


    }

    private function cpmManagerNumberOfAmendmentContracts($sectorIdCondition, $queryString)
    {
        $data = DB::select('
            select 
              count(c.id) as number_of_contracts, a.type_id 
            from contracts as c
            inner join amendments as a
            on c.id = a.contract_id
            ' . $queryString['joins'] . '
            where a.is_approved = true 
            ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . '
            group by a.type_id');
        return $data;
    }


    private function newTransferredContractsBasedOnProcurementEntity($sectorIdCondition, $queryString)
    {

        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $year = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $year = $current_jalali_date[0] + 1;
        }


        $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
        $fiscalEndArray = JDateTime::toGregorian($year, 9, 30);
        $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;
        $fiscalEndDate = $fiscalEndArray ? $fiscalEndArray[0] . '-' . $fiscalEndArray[1] . '-' . $fiscalEndArray[2] : null;

        $statuses_id = DropDowns::getBySlugs(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            ['contract-close-out', 'cancelled']);
        $idQuery = '';
        foreach ($statuses_id as $item) {
            $idQuery .= $item['id'] . ',';
        }
        $idQuery = substr($idQuery, 0, -1);

        $data = DB::select
        ('
             select
                count(case when cp.actual_start_date < "' . $fiscalStartDate . '"  and ( cs.status_id not in (' . $idQuery . ') or cs.status_id is null) then 1 end) as transferred_contracts_count,
                count(case when cp.actual_start_date between "' . $fiscalStartDate . '" and "' . $fiscalEndDate . '" then 1 end) as new_contracts_count,
                c.procurement_entity_id
            from contracts as c
            inner join contract_progresses as cp
                on c.id = cp.contract_id
            join (
                select 
                    csl.id, csl.contract_id, csl.status_id 
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                    on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = cp.contract_id
            ' . $queryString['joins'] . '
            where 1 = 1 
            ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . '
            group by c.procurement_entity_id       
        ');
        return $data;
    }

    private function cpmManagerNumberAndAmountOfContractsBasedOnCompany($sectorIdCondition, $queryString)
    {
        $data = DB::select('
                  select
                com.company_id,
                count(c.id) as contract_count,
                round(sum(
                        ((ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)) * ifnull(com.percentage_contract_share, 100) / 100
                    ) / 1000000 , 4) as total_contracts_amount
                
            from companies as com
            join company_general_informations as cgi
                on cgi.id = com.company_general_information_id
            join contracts as c
                on cgi.contract_id = c.id
            left join contract_details as cd
                on c.id = cd.contract_id
            left join contract_details_verifications cdv
                on cd.id = cdv.contract_detail_id
                and cdv.is_approved = true
            left join (
                 select 
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca 
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    group by  a.contract_id 
            
               ) as amendment
                      on c.id = amendment.contract_id
                      
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $sectorIdCondition . '
            ' . $queryString['query_string'] . '
            group by com.company_id            
        ');
        return $data;
    }

    private function contractChallengesContractAmount($sectorIdCondition, $queryString)
    {
        $data = DB::select('
                     select 
                    car_distinct.category_id,
                    count(car_distinct.contract_id) contracts_count,
                    round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amount
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                   ) as amendment
                          on c.id = amendment.contract_id
               left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
               join (
                    select distinct 
                      inner_car.category_id,
                      inner_car.contract_id
                    from challenges_and_remarks as inner_car
                    where inner_car.is_approved = true
                    group by inner_car.category_id, inner_car.contract_id, inner_car.id
                 )as car_distinct 
                    on car_distinct.contract_id = c.id
                 ' . $queryString['joins'] . '
               where 1 = 1    ' . $sectorIdCondition . '
               ' . $queryString['query_string'] . ' 
               group by car_distinct.category_id'
        );
        return $data;
    }

}

