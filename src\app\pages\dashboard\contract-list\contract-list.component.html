<div #container></div>
<form *ngIf="form" [formGroup]="form" (ngSubmit)="isValid() && submit()">
    <mat-expansion-panel class="search-panel">
        <mat-expansion-panel-header>
            <mat-panel-title>جستجو کنید</mat-panel-title>
        </mat-expansion-panel-header>
        <div *ngIf="form" [formGroup]="form" (ngSubmit)="isValid() && submit()" fxLayout="row wrap"
             fxLayoutAlign="start center" class="search-options">
            <mat-form-field fxFlex='25' *ngIf="authService.user.role.name === 'specialist'">
                <input matInput type="text" formControlName="grant_number" placeholder="شماره اعطاء">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="contract_number" placeholder="شماره قرار داد" lang="en">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="contract_title" placeholder="عنوان قرارداد" lang="en">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input
                        matInput
                        type="text"
                        formControlName="npa_identification_number"
                        placeholder="نمبر تشخیصیه اداره تدارکات ملی"
                        lang="en"
                >
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="number" formControlName="total_amount_from" placeholder="مبلغ کلی قرارداد-از" class="number-input">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="number" formControlName="total_amount_to" placeholder="مبلغ کلی قرارداد-الی" class="number-input">
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <mat-select placeholder="اسعار" formControlName="currency" [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let c of currencies" [value]="c">
                        {{ c.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <mat-select formControlName="donor" placeholder="تمویل کننده" [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let d of donors" [value]="d">{{d?.name_da}}</mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <input matInput type="number" formControlName="physical_improvement_percent_from"
                       placeholder="فیصدی پیشرفت فزیکی تاالحال-از" class="number-input">
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <input matInput type="number" formControlName="physical_improvement_percent_to"
                       placeholder="فیصدی پیشرفت فزیکی تاالحال-الی" class="number-input">
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <input matInput type="number" formControlName="payment_percent_from"
                       placeholder="فیصدی پرداخت تاالحال-از" class="number-input">
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <input matInput type="number" formControlName="payment_percent_to"
                       placeholder="فیصدی پرداخت تاالحال-الی" class="number-input">
            </mat-form-field>


            <div class="npa-form-field" fxFlex="25" *ngIf="
                authService.user.role.name === 'contract-manager' ||
                authService.user.role.name === 'award-authority' ||
                authService.user.role.name === 'cpmd-manager' ">
                <npa-date-time-picker placeholder="تاریخ عقد قرارداد-از(شمسی)"
                                      [(npaDateModel)]="form.controls['agreement_signature_date_from']"
                                      [valueChange]="form.controls['agreement_signature_date_from'].value">
                </npa-date-time-picker>
            </div>
            <mat-form-field fxFlex="25" *ngIf="
                authService.user.role.name === 'contract-manager' ||
                authService.user.role.name === 'award-authority' ||
                authService.user.role.name === 'cpmd-manager' ">
                <input matInput formControlName="agreement_signature_date_from"
                       [matDatepicker]="agreement_signature_date_from"
                       placeholder="تاریخ عقد قرارداد-از(میلادی)">
                <mat-datepicker-toggle matSuffix [for]="agreement_signature_date_from"></mat-datepicker-toggle>
                <mat-datepicker #agreement_signature_date_from></mat-datepicker>
            </mat-form-field>
            <div class="npa-form-field" fxFlex="25" *ngIf="
                authService.user.role.name === 'contract-manager' ||
                authService.user.role.name === 'award-authority' ||
                authService.user.role.name === 'cpmd-manager' ">
                <npa-date-time-picker placeholder="تاریخ عقد قرارداد-الی(شمسی)"
                                      [(npaDateModel)]="form.controls['agreement_signature_date_to']"
                                      [valueChange]="form.controls['agreement_signature_date_to'].value">
                </npa-date-time-picker>
            </div>
            <mat-form-field fxFlex="25" *ngIf="
                authService.user.role.name === 'contract-manager' ||
                authService.user.role.name === 'award-authority' ||
                authService.user.role.name === 'cpmd-manager' ">
                <input matInput formControlName="agreement_signature_date_to"
                       [matDatepicker]="agreement_signature_date_to"
                       placeholder="تاریخ عقد قرارداد-الی(میلادی)">
                <mat-datepicker-toggle matSuffix [for]="agreement_signature_date_to"></mat-datepicker-toggle>
                <mat-datepicker #agreement_signature_date_to></mat-datepicker>
            </mat-form-field>
            <div class="npa-form-field" fxFlex="25" *ngIf="authService.user.role.name === 'specialist'">
                <npa-date-time-picker placeholder="تاریخ آغاز پلانی-از(شمسی)"
                                      [(npaDateModel)]="form.controls['plan_start_date_from']"
                                      [valueChange]="form.controls['plan_start_date_from'].value">
                </npa-date-time-picker>
            </div>
            <mat-form-field fxFlex="25" *ngIf="authService.user.role.name === 'specialist'">
                <input matInput formControlName="plan_start_date_from"
                       [matDatepicker]="plan_start_date_from"
                       placeholder="تاریخ آغاز پلانی-از(میلادی)">
                <mat-datepicker-toggle matSuffix [for]="plan_start_date_from"></mat-datepicker-toggle>
                <mat-datepicker #plan_start_date_from></mat-datepicker>
            </mat-form-field>
            <div class="npa-form-field" fxFlex="25" *ngIf="authService.user.role.name === 'specialist'">
                <npa-date-time-picker placeholder="تاریخ آغاز پلانی-الی(شمسی)"
                                      [(npaDateModel)]="form.controls['plan_start_date_to']"
                                      [valueChange]="form.controls['plan_start_date_to'].value">
                </npa-date-time-picker>
            </div>
            <mat-form-field fxFlex="25" *ngIf="authService.user.role.name === 'specialist'">
                <input matInput formControlName="plan_start_date_to"
                       [matDatepicker]="plan_start_date_to"
                       placeholder="تاریخ آغاز پلانی-الی(میلادی)">
                <mat-datepicker-toggle matSuffix [for]="plan_start_date_to"></mat-datepicker-toggle>
                <mat-datepicker #plan_start_date_to></mat-datepicker>
            </mat-form-field>
            <mat-form-field fxFlex="25">
                <mat-select placeholder="حالت فعلی" formControlName="contract_status"
                            [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let cs of contractStatuses" [value]="cs">
                        {{ cs.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25" *ngIf="
                authService.user.role.name === 'award-authority' ||
                authService.user.role.name === 'specialist' ||
                authService.user.role.name === 'company' ||
                authService.user.role.name === 'cpmd-manager' ">
                <mat-select formControlName="procurement_type" placeholder="نوع تدارکات"
                            [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let pt of procurementTypes" [value]="pt">
                        {{pt.name_da}}
                    </mat-option>

                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25" *ngIf="authService?.user?.role?.name === 'specialist'">
                <mat-select formControlName="contract_manager" placeholder="مسئول قرارداد"
                            [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let cm of contractManagers" [value]="cm.id">
                        {{cm?.full_name}}
                    </mat-option>

                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25" *ngIf="
                authService.user.role.name === 'specialist' ||
                authService.user.role.name === 'cpmd-manager' ||
                authService.user.role.name === 'company' ||
                authService.loggedInUser.role.name === 'cpmd-system-development'">
                <mat-select placeholder="سکتور" formControlName="sector" [compareWith]="formService.selectCompare"
                            (selectionChange)="loadProcurementEntities(form.get('sector').value)">
                    <mat-option *ngFor="let s of sectors" [value]="s">
                        {{ s.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25" *ngIf="
                authService.user.role.name === 'specialist' ||
                authService.user.role.name === 'cpmd-manager' ||
                authService.loggedInUser.role.name === 'cpmd-system-development' ||
                authService.user.role.name === 'company'
            ">
                <mat-select placeholder="نهاد تدارکاتی" formControlName="procurement_entity"
                            [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let pe of procurementEntities" [value]="pe">
                        {{ pe.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <mat-select placeholder="تگ قرارداد(کوید 19)" formControlName="contract_tag"
                            [compareWith]="formService.selectCompare">
                    <mat-option *ngFor="let tag of contactTags" [value]="tag">
                        {{ tag.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <npa-view-element fxFlex="50" *ngIf="authService.user.role.name !== 'company'">
                <div class="npa-label"> نام شرکت قراردادی:</div>
                <div class="npa-value">{{form.get('company_name_da').value}}</div>
            </npa-view-element>
            <div fxFlex='25' *ngIf="authService.user.role.name !== 'company'">
                <button
                        mat-raised-button
                        color="accent"
                        type="button"
                        (click)="openCompanySearchDialog()"
                >انتخاب شرکت قراردادی
                </button>
            </div>


            <div class="checkbox-group" fxLayout="row wrap" fxLayoutAlign="start center">
                <mat-checkbox fxFlex="25" formControlName="is_approved">تائید شده آمر اعطاء</mat-checkbox>
                <mat-checkbox fxFlex="25" formControlName="is_published">نشر شده در وبسایت</mat-checkbox>
                <mat-checkbox fxFlex="25" formControlName="is_transferred">قرار داد انتقالی</mat-checkbox>
                <mat-checkbox fxFlex="25" formControlName="below_threshold">قرار داد های پائین سقف</mat-checkbox>
                <mat-checkbox fxFlex="25" formControlName="above_threshold">قرار داد های بالای سقف</mat-checkbox>
            </div>
        </div>

        <mat-action-row fxLayout="row " fxLayoutAlign="space-between center">
            <div class="search" fxFlex="50" fxLayout="row" fxLayoutAlign="start center">
                <button mat-raised-button
                        color="primary"
                        type="submit"
                        [disabled]="!isValid()|| notificationsService.isLoading">جستجو
                </button>
                <button
                        type="button"
                        mat-button
                        (click)="reset()"
                        [disabled]="notificationsService.isLoading">تنظیم مجدد
                </button>
            </div>
            <div class="actions" fxLayout="row " fxLayoutAlign="end center" fxFlex='50'>
                <button mat-button type="button" (click)="openChooseTitlesDialog()"
                        [disabled]="notificationsService.isLoading">انتخاب عناوین
                </button>
                <!-- <button mat-button type="button" (click)="downloadXlsx()" [disabled]="notificationsService.isLoading">
                    دانلود
                    به فارمت اکسل
                </button> -->
            </div>
        </mat-action-row>
    </mat-expansion-panel>
</form>

<mat-card class="contracts-table-card">
    <mat-card-header>لست قراردادها</mat-card-header>
    <mat-card-content>
        <div class="table-container">
            <mat-table #table *ngIf="dataSource" [dataSource]="dataSource" class="contracts-table">
                <ng-container matColumnDef="contract_number">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('contract_number')">
                        {{contractListProvider.getLabel('contract_number')}}
                    </mat-header-cell>
                    <mat-cell
                            *matCellDef="let row"
                            [class]="contractListProvider.getClass('contract_number')"
                            lang="en"
                    >
                        {{row.contract_number}}
                    </mat-cell>
                </ng-container>
                <!-- <ng-container matColumnDef="npa_identification_number">

                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('npa_identification_number')">
                        {{contractListProvider.getLabel('npa_identification_number')}}
                    </mat-header-cell>
                    <mat-cell
                            *matCellDef="let row"
                            [class]="contractListProvider.getClass('npa_identification_number')"
                            lang="en"
                    >
                        {{row.npa_identification_number}}
                    </mat-cell>
                </ng-container> -->
                <ng-container matColumnDef="name_da">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('name_da')">
                        {{contractListProvider.getLabel('name_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('name_da')">
                        {{row.contract_title}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="donor_name_da">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('donor_name_da')">
                        {{contractListProvider.getLabel('donor_name_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('donor_name_da')">
                        <span *ngFor="let d of row.donors">{{  d.name_da + ', ' + ' ' }}</span>
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="procurement_type_name_da">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('procurement_type_name_da')">
                        {{contractListProvider.getLabel('procurement_type_name_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('procurement_type_name_da')">
                        {{row.procurement_type?.name_da}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="company_name_da">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('company_name_da')">
                        {{contractListProvider.getLabel('company_name_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('company_name_da')">
                        {{row.company_name_da}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="grant_number">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('grant_number')">
                        {{contractListProvider.getLabel('grant_number')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('grant_number')">
                        {{row.grant_number}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="currency">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('currency')">
                        {{contractListProvider.getLabel('currency')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('currency')">
                        {{row.currency?.name_da}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="contract_grand_total_amount">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('contract_grand_total_amount')">
                        {{contractListProvider.getLabel('contract_grand_total_amount')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('contract_grand_total_amount')">
                        {{row.contract_grand_total_amount}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="is_transferred">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('is_transferred')">
                        {{contractListProvider.getLabel('is_transferred')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('is_transferred')">
                        {{row.is_transferred ? 'بلی' : 'نخیر'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="approval_date">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('approval_date')">
                        {{contractListProvider.getLabel('approval_date')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('approval_date')">
                        {{row.approval_date}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="plan_start_date">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('plan_start_date')">
                        {{contractListProvider.getLabel('plan_start_date')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('plan_start_date')">
                        {{row.plan_start_date}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="contract_physical_progress_percentage_up_to_now">

                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('contract_physical_progress_percentage_up_to_now')">
                        {{contractListProvider.getLabel('contract_physical_progress_percentage_up_to_now')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('contract_physical_progress_percentage_up_to_now')">
                        {{(row.contract_physical_progress_percentage_up_to_now ? row.contract_physical_progress_percentage_up_to_now : 0)
                        | number :'.1-2'}}%
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="contract_payment_percentage_up_to_now">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('contract_payment_percentage_up_to_now')">
                        {{contractListProvider.getLabel('contract_payment_percentage_up_to_now')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('contract_payment_percentage_up_to_now')">
                        {{ (row.contract_payment_percentage_up_to_now ? row.contract_payment_percentage_up_to_now : 0) | number
                        :'.1-2'}} %
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="current_status_da">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('current_status_da')">
                        {{contractListProvider.getLabel('current_status_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('current_status_da')">
                        {{row.current_status_da}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="is_published_to_website">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('is_published_to_website')">
                        {{contractListProvider.getLabel('is_published_to_website')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('is_published_to_website')">
                        {{!!row?.is_published ? 'بلی' : 'نخیر'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="procurement_entity_name_da">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('procurement_entity_name_da')">
                        {{contractListProvider.getLabel('procurement_entity_name_da')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('procurement_entity_name_da')">
                        {{row.procurement_entity_name_da}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="contract_manager">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('contract_manager')">
                        {{contractListProvider.getLabel('contract_manager')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('contract_manager')">
                        {{row.contract_manager_name ? row?.contract_manager_name : row?.contract_manager?.full_name}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="specialist">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('specialist')">
                        {{contractListProvider.getLabel('specialist')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('specialist')">
                        <span *ngFor="let specialist of row.specialists">
                            {{specialist?.full_name}}
                        </span>
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="award_authority_approved_percentage">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('award_authority_approved_percentage')">
                        {{contractListProvider.getLabel('award_authority_approved_percentage')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('award_authority_approved_percentage')">
                        {{row.award_authority_approved_percentage}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="contract_manager_confirmed_percentage">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('contract_manager_confirmed_percentage')">
                        {{contractListProvider.getLabel('contract_manager_confirmed_percentage')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('contract_manager_confirmed_percentage')">
                        {{row.contract_manager_confirmed_percentage}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="has_award_authority_approved">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('has_award_authority_approved')">
                        {{contractListProvider.getLabel('has_award_authority_approved')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('has_award_authority_approved')">
                        {{!!row?.is_approved ? 'بلی' : 'نخیر'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="has_contract_manager_confirmed">
                    <mat-header-cell *matHeaderCellDef
                                     [class]="contractListProvider.getClass('has_contract_manager_confirmed')">
                        {{contractListProvider.getLabel('has_contract_manager_confirmed')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row"
                              [class]="contractListProvider.getClass('has_contract_manager_confirmed')">
                        {{!!row?.is_confirmed ? 'بلی' : 'نخیر'}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="options">
                    <mat-header-cell *matHeaderCellDef [class]="contractListProvider.getClass('options')">
                        {{contractListProvider.getLabel('options')}}
                    </mat-header-cell>
                    <mat-cell *matCellDef="let row" [class]="contractListProvider.getClass('options')">
                        <button type='button' mat-icon-button
                                (click)="selectContract(row.id, row.contract_number, row.project_id,row.procurement_type_id)"
                                matTooltip='انتخاب قرارداد'>
                            <mat-icon>open_in_new</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="authService.loggedInUser.role.name === 'cpmd-manager'"
                                matTooltip='تعین کارشناس'
                                (click)="openSpecialistAssignDialog(row)">
                            <mat-icon>person_add</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="authService.loggedInUser.role.slug === 'cpms-cpm-manager' || authService.loggedInUser.role.slug === 'cpms-specialist'"
                                matTooltip='تعین تگ قرارداد'
                                (click)="openProjectTagAssignDialog(row)">
                            <mat-icon>label</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="authService.loggedInUser.role.name === 'contract-director'"
                                matTooltip='تعین مدیر قرارداد'
                                (click)="openContractManagerAssignDialog(row)">
                            <mat-icon>person_add</mat-icon>
                        </button>


                        <button type='button' mat-icon-button
                                *ngIf="authService.loggedInUser.role.name === 'contract-manager' && !row.is_confirmed"
                                matTooltip='تائید معلومات پلان گذرای قرارداد'
                                (click)="confirmContractPlanning(row)">
                            <mat-icon>done</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="
                                    authService.loggedInUser.role.name === 'contract-manager' && !!row.is_confirmed && !row.is_approved"
                                matTooltip='فسخ تائیدی معلومات پلان گذرای قرارداد'
                                (click)="requestToUnConfirmContractPlanning(row)">
                            <mat-icon>reply</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="
                                    authService.loggedInUser.role.name === 'contract-manager'
                                    && !!row.is_confirmed && !!row.is_approved && !row.has_requested_to_unapprove"
                                matTooltip='درخواست فسخ منظوری معلومات پلان گذرای قرارداد'
                                (click)="requestToUnApproveContractPlanning(row)">
                            <mat-icon>undo</mat-icon>
                        </button>


                        <button type='button' mat-icon-button
                                *ngIf="
                                        authService.loggedInUser.role.name === 'award-authority' && !!row.is_confirmed && !row.is_approved"
                                matTooltip='منظوری معلومات پلان گذرای قرارداد'
                                (click)="approveContractPlanning(row)">
                            <mat-icon>done_all</mat-icon>
                        </button>

                        <button type='button' mat-icon-button
                                *ngIf="
                                        authService.loggedInUser.role.name === 'award-authority' && !!row.is_approved && !row.is_published"
                                matTooltip='نشر در ویب سایت معلومات پلان گذرای قرارداد'
                                (click)="publishContractPlanning(row)">
                            <mat-icon>publish</mat-icon>
                        </button>


                        <button type='button' mat-icon-button
                                *ngIf="
                                    authService.loggedInUser.role.name === 'award-authority' && !!row.is_published
                                    && !row.has_requested_to_unpublish"
                                matTooltip='درخواست فسخ نشر در ویب سایت معلومات پلان گذرای قرارداد'
                                (click)="requestToUnPublishContractPlanning(row)">
                            <mat-icon>remove_circle_outline</mat-icon>
                        </button>

                        <button type='button' mat-icon-button [disabled]="!!row.is_published"
                                *ngIf="
                                    authService.loggedInUser.role.name === 'award-authority' &&
                                    !!row.has_requested_to_unapprove && !!row.is_approved"
                                matTooltip='پاسخ به درخواست فسخ منظوری معلومات پلان گذرای قرارداد'
                                (click)="respondToRequestToUnApproveContractPlanning(row)">
                            <mat-icon>reply_all</mat-icon>
                        </button>


                        <button type='button' mat-icon-button
                                *ngIf="
                                    (
                                        authService.loggedInUser.role.name === 'cpmd-manager'
                                    ) && !!row.has_requested_to_unpublish && !!row.is_published
                                "
                                matTooltip='پاسخ به درخواست فسخ نشر در ویب سایت معلومات پلان گذرای قرارداد'
                                (click)="respondToRequestToUnPublishContractPlanning(row)">
                            <mat-icon>reply</mat-icon>
                        </button>
                        <button type='button' mat-icon-button
                                *ngIf="
                                        authService.loggedInUser.role.name === 'cpmd-manager'"
                                matTooltip='اجازه تصحیح قرارداد'
                                (click)="contractEditRight(row?.id)">
                            <mat-icon>spellcheck</mat-icon>
                        </button>
                    </mat-cell>
                </ng-container>

                <mat-header-row *matHeaderRowDef="chosenTitles" [style.width]="getWidth()"></mat-header-row>
                <mat-row *matRowDef="let row; columns: chosenTitles" [style.width]="getWidth()"></mat-row>
            </mat-table>
        </div>
        <mat-paginator></mat-paginator>
    </mat-card-content>
</mat-card>
