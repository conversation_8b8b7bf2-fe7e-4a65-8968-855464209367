import {Component, OnInit} from '@angular/core';
import {AuthService} from '../../../services/auth.service';
import {NotificationsService} from '../../shared/services/notifications.service';
import {ContractListProvider} from '../contract-list/contract-list.provider';
import {DropDownsService} from '../../../services/dropDowns.service';
import {FormBuilder, FormGroup} from '@angular/forms';
import {IForm} from '../../shared/types/general.types';
import {ChartsProvider} from './charts.provider';


@Component({
    selector: 'npa-charts',
    templateUrl: './charts.component.html',
    styleUrls: ['./charts.component.styl']
})
export class ChartsComponent implements OnInit, IForm {
    form: FormGroup;
    expanded;
    procurementTypes = [];
    contractManagers = [];
    specialists = [];
    awardAuthorities = [];
    cpmManagers = [];
    amendmentTypes = [];
    donors = [];
    selectionMethods = [];
    zones = [];
    provinces = [];
    districts = [];
    contractStatuses = [];
    procurementMethods = [];
    procurementEntities = [];
    sectors = [];
    chartData = null;

    constructor(public authService: AuthService,
                public notificationsService: NotificationsService,
                public contractListProvider: ContractListProvider,
                private _formBuilder: FormBuilder,
                private _dropDownsService: DropDownsService,
                private _chartsProvider: ChartsProvider) {
    }

    ngOnInit() {
        if (this.authService.loggedInUser.role.name === 'award-authority' || this.authService.loggedInUser.role.name === 'assigner-publisher') {
            this.loadContractManager(null);
        }
        if (this.authService.loggedInUser.role.name === 'cpmd-manager') {
            this.loadSpecialist(null);
            this.loadContractManager(null);
            this._chartsProvider.getAllAwardAuthorities().subscribe(list => {
                this.awardAuthorities = list;
            });

            this._chartsProvider.getAllCpmManagers().subscribe(list => {
                this.cpmManagers = list;
            });
        }
        this.authService.currentActingRole.subscribe(() => {
            this.notificationsService.startLoading();
            this._chartsProvider.provideChartsDataAndFilter().subscribe(data => {
                this.chartData = Object.keys(data).length > 0 && data;
                this.notificationsService.dismissLoading();
            });
        });
        this.procurementTypes = this._dropDownsService.getAll('procurementType');
        this.amendmentTypes = this._dropDownsService.getAll('amendmentType');
        this.donors = this._dropDownsService.getAll('donor');
        this.selectionMethods = this._dropDownsService.getAll('selectionMethod');
        this.zones = this._dropDownsService.getAll('zone');
        this.contractStatuses = this._dropDownsService.getAll('contractStatus');
        this.procurementMethods = this._dropDownsService.getAll('procurementMethod');
        this.sectors = this._dropDownsService.getAll('sectorAndProcurementEntity/sector');
        this.initForm();
    }

    loadSpecialist(cpmManagerId) {
        this._chartsProvider.getAllSpecialists(cpmManagerId).subscribe(list => {
            this.specialists = list;
        });
    }

    loadContractManager(awardAuthorityId) {
        this._chartsProvider.getAllContractManagers(awardAuthorityId).subscribe(list => {
            this.contractManagers = list;
        });
    }

    changeToBelowThreshold() {
        if (this.form.get('below_threshold').value === true) {
            this.form.get('above_threshold').setValue(false);
        }
    }

    changeToAboveThreshold() {
        if (this.form.get('above_threshold').value === true) {
            this.form.get('below_threshold').setValue(false);
        }
    }

    initForm() {
        this.form = this._formBuilder.group({
            procurement_type: [],
            contract_manager_id: [],
            above_threshold: [],
            below_threshold: [],
            amendment_type: [],
            cost_amendment_percentage_start: [],
            cost_amendment_percentage_end: [],
            time_amendment_percentage_start: [],
            time_amendment_percentage_end: [],
            donor: [],
            selection_method: [],
            zone: [],
            province: [],
            district: [],
            contract_status: [],
            procurement_method: [],
            contract_total_value_start: [],
            contract_total_value_end: [],
            actual_payments_value_start: [],
            actual_payments_value_end: [],
            actual_payments_physical_percentage_start: [],
            actual_payments_physical_percentage_end: [],
            specialist_id: [],
            award_authority_id: [],
            cpm_manager_id: [],
            fiscal_year_start: [],
            fiscal_year_end: [],
            procurement_entity_id: [],
            sector_id: []
        });
    }

    isValid() {
        return this.form.valid;
    }

    submit(formData) {
        for (const key in formData) {
            if (formData.hasOwnProperty(key) && !formData[key]) {
                delete (formData[key]);
            }
        }
        this.notificationsService.startLoading();
        this._chartsProvider.provideChartsDataAndFilter(formData).subscribe(data => {
            this.chartData = data;
            this.expanded = false;
            this.notificationsService.dismissLoading();
        });
    }

    loadProvinces(zoneId: number) {
        this.form.get('district').reset();
        this.form.get('province').reset();
        this.provinces = this
            ._dropDownsService
            .getWithCondition('province', 'zone_id', zoneId);
    }


    loadProcurementEntities(sectorId: number) {
        this.procurementEntities = this
            ._dropDownsService
            .getWithCondition('sectorAndProcurementEntity/procurementEntity', 'sector_id', sectorId);
    }
}
