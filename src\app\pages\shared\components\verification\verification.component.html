<mat-card *ngIf="!isRowVerification && !isInCardVerification">
    <form>
        <mat-card-header>تائیدی و منظوری</mat-card-header>
        <mat-card-content>
            <div fxLayout="row wrap" fxLayoutAlign="start">
                <npa-view-element fxFlex="33">
                    <div class="npa-label">
                        تائید {{confirmationAuthority}}:
                    </div>
                    <div class="npa-value">{{!!verificationData?.is_confirmed ? 'بلی': 'نخیر'}}</div>
                </npa-view-element>

                <npa-view-element fxFlex="33">
                    <div class="npa-label">
                        منظوری {{approvalAuthority}}:
                    </div>
                    <div class="npa-value">{{!!verificationData?.is_approved ? 'بلی': 'نخیر'}}</div>
                </npa-view-element>

                <npa-view-element fxFlex="33" *ngIf="canPublish">
                    <div class="npa-label">نشر در وب سایت:</div>
                    <div class="npa-value">{{!!verificationData?.is_published ? 'بلی': 'نخیر'}}</div>
                </npa-view-element>
            </div>
        </mat-card-content>
        <mat-card-actions>

            <button mat-raised-button
                    [disabled]="notificationsService.isLoading"
                    *ngIf="(
                        (authService.loggedInUser.role.name ==='award-authority' && isProcurementEntityConcerned) ||
                        (authService.loggedInUser.role.name ==='cpmd-manager' && !isProcurementEntityConcerned)
                    ) &&
                    !!verificationData?.is_confirmed && !verificationData?.is_published"
                    class="button-card-action"
                    (click)="approve()">
                {{!!verificationData?.is_approved ?
                ' فسخ منظوری '+approvalAuthority :
                'منظوری '+approvalAuthority}}
            </button>

            <button mat-raised-button
                    [disabled]="!!verificationData?.is_confirmed && !unconfirmation || notificationsService.isLoading"
                    *ngIf="
                            (
                                (authService.loggedInUser.role.name === 'contract-manager' && isProcurementEntityConcerned) ||
                                (authService.loggedInUser.role.name === 'specialist' && !isProcurementEntityConcerned)
                            ) &&
                            canConfirm &&
                           !verificationData?.is_approved && !(isPlanning * contractService.isContractPlanningGenerallyConfirmed)"
                    (click)="confirm()">
                {{!!verificationData?.is_confirmed && unconfirmation ?
                'فسخ تائید ' + confirmationAuthority
                : 'تائید ' + confirmationAuthority}}
            </button>

            <button mat-raised-button
                    *ngIf="authService.loggedInUser.role.name ==='award-authority' &&
                    canPublish && !!verificationData?.is_approved"
                    class="button-card-action"
                    [disabled]="!!verificationData?.is_published || notificationsService.isLoading"
                    (click)="awardAuthorityIsPublishedApproval()"> نشر در وب سایت
            </button>
        </mat-card-actions>
    </form>
</mat-card>

<div class="row-verification-wrapper" *ngIf="isRowVerification">
    <button mat-icon-button
            [disabled]="notificationsService.isLoading"
            *ngIf="authService.loggedInUser.role.name ==='award-authority' &&
            !!verificationData?.is_confirmed &&
            (!canPublish ? !verificationData?.is_published : !canPublish)"
            [matTooltip]="!!verificationData?.is_approved ? 'فسخ منظوری آمر اعطاء' : 'منظوری آمر اعطاء'"
            (click)="approve()">
        <mat-icon>{{!!verificationData?.is_approved ? 'undo' : 'check_circle'}}</mat-icon>
    </button>
    <button mat-icon-button
            [disabled]="!!verificationData?.is_confirmed && !unconfirmation || notificationsService.isLoading"
            *ngIf="canConfirm && authService.loggedInUser.role.name ==='contract-manager' &&
            !verificationData?.is_approved && !(isPlanning * contractService.isContractPlanningGenerallyConfirmed) &&
            (!canPublish || canSpecificPublish)"
            [matTooltip]="!!verificationData?.is_confirmed ? 'فسخ تائید مدیر قرارداد' : 'تائید مدیر قرارداد'"
            (click)="confirm()">
        <mat-icon>{{!!verificationData?.is_confirmed && unconfirmation ? 'undo' : 'check_circle'}}</mat-icon>
    </button>

    <button mat-icon-button
            *ngIf="(canPublish || canSpecificPublish && !!verificationData?.is_approved) && authService.loggedInUser.role.name ==='award-authority'"
            [disabled]="!!verificationData?.is_published && !!verificationData?.is_approved || notificationsService.isLoading"
            matTooltip="نشر در وب سایت"
            (click)="awardAuthorityIsPublishedApproval()">
        <mat-icon>publish</mat-icon>
    </button>

    <button mat-icon-button
            [disabled]="notificationsService.isLoading"
            *ngIf="canPublish &&
                   (!!verificationData?.is_published &&
                   (authService.loggedInUser.role.name ==='award-authority' || authService.loggedInUser.role.name ==='assigner-publisher')) ||
                   (authService.loggedInUser.role.name ==='cpmd-manager')"
            [disabled]="(verificationData?.has_requested_to_unpublish &&
                        (authService.loggedInUser.role.name ==='award-authority' || authService.loggedInUser.role.name ==='assigner-publisher')) ||
                        (!verificationData?.has_requested_to_unpublish &&
                        authService.loggedInUser.role.name ==='cpmd-manager')"
            [matTooltip]="(authService.loggedInUser.role.name ==='award-authority' || authService.loggedInUser.role.name ==='assigner-publisher') ? 'درخواست لغو نشر در ویب سایت' : 'پاسخ به درخواست لغو نشر در ویب سایت'"
            (click)="awardAuthorityUnpublishedRequest()">
        <mat-icon>input</mat-icon>
    </button>
</div>

<div *ngIf="isInCardVerification">
    <form>
        <div fxLayout="row wrap" fxLayoutAlign="start">
            <npa-view-element fxFlex="33">
                <div class="npa-label">
                    تائید {{confirmationAuthority}}:
                </div>
                <div class="npa-value">{{!!verificationData?.is_confirmed ? 'بلی': 'نخیر'}}</div>
            </npa-view-element>

            <npa-view-element fxFlex="33">
                <div class="npa-label">
                    منظوری {{approvalAuthority}}:
                </div>
                <div class="npa-value">{{!!verificationData?.is_approved ? 'بلی': 'نخیر'}}</div>
            </npa-view-element>

            <npa-view-element fxFlex="33" *ngIf="canPublish">
                <div class="npa-label">نشر در وب سایت:</div>
                <div class="npa-value">{{!!verificationData?.is_published ? 'بلی': 'نخیر'}}</div>
            </npa-view-element>
        </div>
        <button mat-raised-button
                [disabled]="notificationsService.isLoading"
                *ngIf="(
                        ((authService.loggedInUser.role.name ==='award-authority' || authService.loggedInUser.role.name ==='assigner-publisher') && isProcurementEntityConcerned) ||
                        (authService.loggedInUser.role.name ==='cpmd-manager' && !isProcurementEntityConcerned)
                    ) &&
                    !!verificationData?.is_confirmed && !verificationData?.is_published"
                class="button-card-action"
                (click)="approve()">
            {{!!verificationData?.is_approved ?
            ' فسخ منظوری '+approvalAuthority :
            'منظوری '+approvalAuthority}}
        </button>

        <button mat-raised-button
                [disabled]="!!verificationData?.is_confirmed && !unconfirmation || notificationsService.isLoading"
                *ngIf="
                            (
                                (authService.loggedInUser.role.name === 'contract-manager' && isProcurementEntityConcerned) ||
                                (authService.loggedInUser.role.name === 'specialist' && !isProcurementEntityConcerned)
                            ) &&
                            canConfirm &&
                           !verificationData?.is_approved && !(isPlanning * contractService.isContractPlanningGenerallyConfirmed)"
                (click)="confirm()">
            {{!!verificationData?.is_confirmed && unconfirmation ?
            'فسخ تائید ' + confirmationAuthority
            : 'تائید ' + confirmationAuthority}}
        </button>

        <button mat-raised-button
                *ngIf="authService.loggedInUser.role.name ==='award-authority' &&
                    canPublish && !!verificationData?.is_approved"
                class="button-card-action"
                [disabled]="!!verificationData?.is_published || notificationsService.isLoading"
                (click)="awardAuthorityIsPublishedApproval()"> نشر در وب سایت
        </button>
    </form>
</div>
