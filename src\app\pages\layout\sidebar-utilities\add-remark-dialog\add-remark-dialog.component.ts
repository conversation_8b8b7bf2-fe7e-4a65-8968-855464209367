import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON>er, FormGroup} from '@angular/forms';
import {ContractService} from '../../../../services/contract.service';
import {Router} from '@angular/router';
import {AuthService} from '../../../../services/auth.service';
import {NotificationsService} from '../../../shared/services/notifications.service';
import {
    IRemark, IRemarkCandidateRecipient, IRemarkMessage, IRemarkRecipient,
    IRemarkRecipientServer
} from '../../../remark/remark.types';
import {FormService} from '../../../shared/services/form.service';
import {IDropdown} from '../../../shared/types/general.types';
import {DropDownsService} from '../../../../services/dropDowns.service';
import {RemarkProvider} from '../remark.provider';
import {HttpErrorResponse, HttpResponse} from '@angular/common/http';
import {NPA_ALERTS} from '../../../shared/consts/messages';
import {IAlertRecipientRole} from '../../../alert/alert.types';
import {MatDialogRef} from '@angular/material/dialog';
import {FormValidationService} from '../../../shared/services/form-validation.service';

@Component({
    selector: 'npa-add-remark-dialog',
    templateUrl: './add-remark-dialog.component.html',
    styleUrls: ['./add-remark-dialog.component.styl']
})
export class AddRemarkDialogComponent implements OnInit {
    form: FormGroup;
    alert_subcategories: IDropdown[];
    optionalRoles: FormGroup[] = [];
    targetedRoles: IRemarkRecipient[] = [];

    constructor(private _formBuilder: FormBuilder,
                private _contractService: ContractService,
                private _router: Router,
                private _authService: AuthService,
                public notificationsService: NotificationsService,
                public formValidationService: FormValidationService,
                public formService: FormService,
                private _dropDownsService: DropDownsService,
                private _remarkProvider: RemarkProvider,
                private _dialogRef: MatDialogRef<AddRemarkDialogComponent>) {
    }

    ngOnInit() {
        this.initForm();
        const category = this._dropDownsService.getBySlug('alertsAndRemarks/category', 'remarks-cpms');
        if (this._authService.loggedInUser.role.name === 'cpmd-manager' || this._authService.loggedInUser.role.name === 'specialist') {
            this.alert_subcategories = this._dropDownsService.getWithCondition(
                'alertsAndRemarks/subcategory',
                'alert_category_id',
                category.id
            );
        } else {
            this.alert_subcategories = [this._dropDownsService.getBySlug(
                'alertsAndRemarks/subcategory',
                'cpms-remarks-miscellaneous'
            )];
        }
    }

    initForm() {
        const url = this._router.url.split('/').slice(2).join('/');

        this.form = this._formBuilder.group(<IRemark>{
            id: undefined,
            contract_name_da: undefined,
            title: [undefined, this.formValidationService.required.validator],
            contract_number: this._contractService.contractCode,
            contract_id: this._contractService.contractInfo.value['id'],
            url: url,
            message: this._formBuilder.group(<IRemarkMessage>{
                id: undefined,
                alert_id: undefined,
                contents: [undefined, this.formValidationService.required.validator],
                creator_user_id: this._authService.loggedInUser.id,
                creator_user_full_name: this._authService.loggedInUser.full_name,
                created_at: undefined
            }),
            recipients: undefined,
            candidate_recipients: undefined,
            alert_subcategory: undefined
        });
    }

    submit() {
        this.notificationsService.startLoading();
        this._remarkProvider.store(this.form.getRawValue()).subscribe((response: HttpResponse<any>) => {
            this.notificationsService.success(NPA_ALERTS.ADD_SUCCESS);
            this.form.get('id').setValue(response.headers.get('location'));
            this.form.get('contract_name_da').setValue(response.body.contract_name_da);

            this._dialogRef.close(this.form.getRawValue());
        }, (error: HttpErrorResponse) => {
            this.notificationsService.error(NPA_ALERTS.ERROR);
            console.error(error);
        });
    }

    isValid(): boolean {
        let isValid = this.form.valid;

        const areAnyCandidateRecipientsSelected = !!this.form.get('candidate_recipients').value &&
            this.form.get('candidate_recipients').value.filter((current: IRemarkCandidateRecipient) => {
                return current.is_selected;
            }).length;

        const areThereAnyTargetedRecipients = !!this.form.get('recipients').value &&
            this.form.get('recipients').value.length;

        isValid = isValid && (areAnyCandidateRecipientsSelected || areThereAnyTargetedRecipients);

        return isValid;
    }

    loadRecipients() {
        this.optionalRoles = [];
        this.targetedRoles = [];

        const alertSubcategory = this.form.get('alert_subcategory').value;

        this.notificationsService.startLoading();
        this._contractService.getRoleUsersMap(this._contractService.contractInfo.value['id']).subscribe(
            (recipientsOnServer: IRemarkRecipientServer) => {

                const tempOptionalRoles: IAlertRecipientRole[] = this._dropDownsService.getWithCondition(
                    'alertsAndRemarks/alertOptionalRole',
                    'alert_subcategory_id',
                    alertSubcategory.id
                );
                tempOptionalRoles.forEach((currentOptionalRole: IAlertRecipientRole) => {
                    const foundUserRole = recipientsOnServer[currentOptionalRole.role.slug];
                    if (foundUserRole) {
                        if (this.shouldAddConsideringMiscCategory(alertSubcategory, foundUserRole)) {
                            this.optionalRoles.push(this._formBuilder.group(<IRemarkRecipient>{
                                user_id: foundUserRole.user_id,
                                user_full_name: foundUserRole.full_name,
                                role_name_da: currentOptionalRole.role.name_da,
                                role_slug: currentOptionalRole.role.slug,
                                is_targeted: false,
                                is_selected: false
                            }));
                        }
                    }

                });
                this.form.controls['candidate_recipients'] = this._formBuilder.array(this.optionalRoles);

                // ============

                const tempTargetedRoles: IAlertRecipientRole[] = this._dropDownsService.getWithCondition(
                    'alertsAndRemarks/alertTargetedRole',
                    'alert_subcategory_id',
                    alertSubcategory.id
                );
                tempTargetedRoles.forEach((currentTargetedRole: IAlertRecipientRole) => {
                    const foundUserRole = recipientsOnServer[currentTargetedRole.role.slug];
                    if (foundUserRole) {
                        this.targetedRoles.push(<IRemarkRecipient>{
                            user_id: foundUserRole.user_id,
                            user_full_name: foundUserRole.user_full_name,
                            role_name_da: currentTargetedRole.role.name_da,
                            role_slug: currentTargetedRole.role.slug,
                            is_targeted: true
                        });
                    }
                });
                let hasLoggedInUser = false;
                const role = this._dropDownsService.getBySlug('role', this._authService.loggedInUser.role.slug);
                this.targetedRoles.forEach((targetedRole) => {
                    if (targetedRole.user_id === this._authService.loggedInUser.id) {
                        hasLoggedInUser = true;
                    }
                });
                if (!hasLoggedInUser) {
                    this.targetedRoles.push(<IRemarkRecipient>{
                        user_id: this._authService.loggedInUser.id,
                        user_full_name: this._authService.loggedInUser.full_name,
                        role_name_da: role ? role.name_da : '',
                        role_slug: this._authService.loggedInUser.role.name,
                        is_targeted: true
                    });
                }
                this.form.controls['recipients'] = this._formBuilder.array(this.targetedRoles);

                this.notificationsService.dismissLoading();

            },
            (error: HttpErrorResponse) => {
                this.notificationsService.error(NPA_ALERTS.ERROR);
                console.error(error);
            }
        );
    }

    private shouldAddConsideringMiscCategory(alertSubcategory: any, foundUserRole: any) {
        return (
            alertSubcategory.slug === 'cpms-remarks-miscellaneous' &&
            foundUserRole.user_id !== this._authService.loggedInUser.id
        ) || alertSubcategory.slug !== 'cpms-remarks-miscellaneous';
    }

    closeDialog() {
        this._dialogRef.close(false);
    }
}
