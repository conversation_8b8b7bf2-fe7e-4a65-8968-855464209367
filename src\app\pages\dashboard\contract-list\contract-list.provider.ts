import {Injectable} from '@angular/core';
import {HttpService} from '../../../services/http.service';
import {Observable} from 'rxjs';
import {ITableHeader} from './contract-list.types';
import {environment as localEnvironment} from '../../../../environments/environment';
import {environment as productionEnvironment} from '../../../../environments/environment.prod';

@Injectable()
export class ContractListProvider {

    private _slugLabelMap: any = {};
    private _slugClassMap: any = {};
    private _slugWidthMultiplicationFactorMap: any = {};
    env = localEnvironment || productionEnvironment;

    constructor(private _httpService: HttpService) {

    }

    index(pageIndex: number, pageSize: number, searchParameters?: any): Observable<any> {
        searchParameters = this.prepareDataFormat(searchParameters);
        searchParameters.page_size = pageSize;
        searchParameters.page_index = pageIndex;
        return this._httpService.get(`api/contracts`, {
            params: searchParameters,
            observe: 'response'
        });
    }

    prepareDataFormat(data) {
        for (const property in data) {
            if (data.hasOwnProperty(property)) {
                if (data[property] instanceof Date) {
                    data[property] = data[property].getFullYear() + '-' + (data[property].getMonth() + 1) + '-' + data[property].getDate();
                }
                const child = data[property];
                if (typeof data[property] === 'object' && data[property] instanceof Date === false) {
                    for (const field in <Object>child) {
                        if (child.hasOwnProperty(field) && child[field] instanceof Date) {
                            child[field] = child[field].getFullYear() + '-' + (child[field].getMonth() + 1) + '-' + child[field].getDate();
                        }
                    }
                }
            }
        }
        return data;
    }

    getAllContractMangers(): Observable<any> {
        return this._httpService.get('api/get-all-contract-managers');
    }

    getLabel(slug: string) {
        if (!this._slugLabelMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugLabelMap[current.slug] = current.label_da;
            });
        }
        return this._slugLabelMap[slug];
    }

    getClass(slug: string) {
        if (!this._slugClassMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugClassMap[current.slug] = `widthUnit${current.width_multiplication_factor}`;
            });
        }
        return this._slugClassMap[slug];
    }

    getWidthMultiplicationFactor(slug: string) {
        if (!this._slugWidthMultiplicationFactorMap.init_marker) {
            CONTRACT_LIST_TITLES.forEach((current: ITableHeader) => {
                this._slugWidthMultiplicationFactorMap[current.slug] = current.width_multiplication_factor;
            });
        }
        return this._slugWidthMultiplicationFactorMap[slug];
    }

    getDisplayedColumns(userRole: string, loggedInUserRole: string) {
        if (userRole === 'company' || loggedInUserRole === 'company') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'company_name_da',
                'procurement_type_name_da',
                'contract_grand_total_amount',
                'currency',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'is_transferred',
                'approval_date',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
            ];
        } else if (userRole === 'award-authority' || loggedInUserRole === 'award-authority' || userRole === 'observer' || loggedInUserRole === 'observer' || userRole === 'contract-director' || loggedInUserRole === 'contract-director' || userRole === 'cpms-assigner-publisher' || loggedInUserRole === 'cpms-assigner-publisher') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'company_name_da',
                'procurement_type_name_da',
                'donor_name_da',
                'contract_grand_total_amount',
                'currency',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'is_transferred',
                'approval_date',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        } else if (userRole === 'contract-manager' || loggedInUserRole === 'contract-manager') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'company_name_da',
                'contract_grand_total_amount',
                'approval_date',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        } else if (
            userRole === 'award-award_authority_approved_percentage' ||
            loggedInUserRole === 'award-award_authority_approved_percentage'
            // || userRole === 'cpmd-manager' || loggedInUserRole === 'cpmd-manager'
        ) {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'procurement_type_name_da',
                'company_name_da',
                'contract_grand_total_amount',
                'approval_date',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        } else if (userRole === 'specialist' || loggedInUserRole === 'specialist') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'procurement_type_name_da',
                'donor_name_da',
                'procurement_entity_name_da',
                'grant_number',
                'company_name_da',
                'contract_grand_total_amount',
                'plan_start_date',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        } else if (userRole === 'cpmd-manager' || loggedInUserRole === 'cpmd-manager') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'procurement_type_name_da',
                'donor_name_da',
                'procurement_entity_name_da',
                'grant_number',
                'company_name_da',
                'contract_grand_total_amount',
                'plan_start_date',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        } else if (userRole === 'cpmd-system-development' || loggedInUserRole === 'cpmd-system-development') {
            return [
                'options',
                'contract_number',
                // 'npa_identification_number',
                'name_da',
                'procurement_type_name_da',
                'donor_name_da',
                'procurement_entity_name_da',
                'grant_number',
                'company_name_da',
                'contract_grand_total_amount',
                'plan_start_date',
                'contract_physical_progress_percentage_up_to_now',
                'contract_payment_percentage_up_to_now',
                'current_status_da',
                'is_published_to_website',
                'specialist',
                'contract_manager',
                'has_award_authority_approved',
                'has_contract_manager_confirmed',
                'award_authority_approved_percentage',
                'contract_manager_confirmed_percentage',
            ];
        }
    }

    confirmContractPlanningRequest(id: number): Observable<any> {
        return this._httpService.post('api/contractList/confirmContractPlanningInfo?id=' + id, id);
    }

    requestToUnConfirmContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/unConfirmContractPlanningInfo?id=' + id, id);
    }

    requestToUnApproveContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/requestToUnApproveContractPlanning?id=' + id, id);
    }

    approveContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/approveContractPlanning?id=' + id, id);
    }

    publishContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/publishContractPlanning?id=' + id, id);
    }

    requestToUnPublishContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/requestToUnPublishContractPlanning?id=' + id, id);
    }

    respondToRequestToUnApproveContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/respondToRequestToUnApproveContractPlanning?id=' + id, id);
    }

    respondToRequestToUnPublishContractPlanning(id: number): Observable<any> {
        return this._httpService.post('api/contractList/respondToRequestToUnPublishContractPlanning?id=' + id, id);
    }

    downloadXlsx(data): Observable<Blob> {
        return this._httpService.postSpecial(`api/contracts/download/xlsx`,
            data,
            {responseType: 'blob', observe: 'response'}
        );
    }

    removeReadOnlySectionsVerification(id): Observable<any> {
        return this._httpService.delete('/api/remove/read-only-sections/verification/' + id);
    }


}

export const CONTRACT_LIST_TITLES: ITableHeader[] = [
    {
        slug: 'init_marker',
        label_da: 'init_marker',
        width_multiplication_factor: 0
    },
    {
        slug: 'contract_manager',
        label_da: 'مدیر قرارداد',
        width_multiplication_factor: 1
    },
    {
        slug: 'plan_start_date',
        label_da: 'تاریخ آغاز پلانی',
        width_multiplication_factor: 1
    },
    {
        slug: 'grant_number',
        label_da: 'شماره اعطاء',
        width_multiplication_factor: 1
    },
    // {
    //     slug: 'npa_identification_number',
    //     label_da: 'نمبر تشخیصیه',
    //     width_multiplication_factor: 2
    // },
    {
        slug: 'contract_number',
        label_da: 'نمبر قرارداد',
        width_multiplication_factor: 2
    },
    {
        slug: 'name_da',
        label_da: ' نام قرارداد',
        width_multiplication_factor: 3
    },
    {
        slug: 'company_name_da',
        label_da: 'نام شرکت قراردادی',
        width_multiplication_factor: 2
    },
    {
        slug: 'procurement_type_name_da',
        label_da: 'نوع تدارکات',
        width_multiplication_factor: 2
    },
    {
        slug: 'procurement_entity_name_da',
        label_da: 'نهاد مربوطه',
        width_multiplication_factor: 2
    },
    {
        slug: 'donor_name_da',
        label_da: 'منبع تمویل کننده',
        width_multiplication_factor: 2
    },
    {
        slug: 'contract_grand_total_amount',
        label_da: 'مبلغ کلی قرارداد',
        width_multiplication_factor: 1
    },
    {
        slug: 'currency',
        label_da: 'واحد پول',
        width_multiplication_factor: 1
    },
    {
        slug: 'contract_physical_progress_percentage_up_to_now',
        label_da: 'فیصدی پیشرفت فزیکی تاالحال',
        width_multiplication_factor: 1
    },
    {
        slug: 'contract_payment_percentage_up_to_now',
        label_da: 'فیصدی پرداخت تاالحال',
        width_multiplication_factor: 1
    },
    {
        slug: 'current_status_da',
        label_da: 'حالت فعلی',
        width_multiplication_factor: 1
    },
    {
        slug: 'is_published_to_website',
        label_da: 'نشر در ویب سایت',
        width_multiplication_factor: 1
    },
    {
        slug: 'approval_date',
        label_da: 'تاریخ منظوری',
        width_multiplication_factor: 1
    },
    {
        slug: 'is_transferred',
        label_da: 'قرار داد انتقالی',
        width_multiplication_factor: 1
    },
    {
        slug: 'specialist',
        label_da: 'کارشناس ها',
        width_multiplication_factor: 2
    },
    {
        slug: 'contract_manager_confirmed_percentage',
        label_da: 'فیصدی تائیدی مدیر قرارداد',
        width_multiplication_factor: 2
    },
    {
        slug: 'award_authority_approved_percentage',
        label_da: 'فیصدی منظوری آمر اعطاء',
        width_multiplication_factor: 1
    },
    {
        slug: 'has_award_authority_approved',
        label_da: 'منظوری آمر اعطاء؟',
        width_multiplication_factor: 1
    },
    {
        slug: 'has_contract_manager_confirmed',
        label_da: 'تائیدی مدیر قرارداد؟',
        width_multiplication_factor: 1
    },
    {
        slug: 'options',
        label_da: 'اختیارات',
        width_multiplication_factor: 2
    }
];
