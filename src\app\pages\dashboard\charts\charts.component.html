<form *ngIf="form" [formGroup]="form" (ngSubmit)="submit(form.value)">
    <mat-expansion-panel [expanded]="expanded" (opened)="expanded = true">
        <mat-expansion-panel-header>
            <mat-panel-title>فلتر کنید</mat-panel-title>
        </mat-expansion-panel-header>
        <div fxLayout="row wrap" fxLayoutAlign="start center" class="search-options">

            <mat-form-field fxFlex="25">
                <mat-select formControlName="procurement_type" placeholder="نوع تدارکات">
                    <mat-option *ngFor="let procurementType of procurementTypes"
                                [value]="procurementType?.id">
                        {{ procurementType?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25" *ngIf="form?.get('procurement_type').value === 4">
                <mat-select formControlName="selection_method" placeholder="روش انتخاب">
                    <mat-option *ngFor="let selectionMethod of selectionMethods"
                                [value]="selectionMethod?.id">
                        {{ selectionMethod?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex="25" *ngIf="authService.loggedInUser.role.name === 'cpmd-manager'
                                               || authService.loggedInUser.role.name === 'cpmd-system-development'">
                <mat-select formControlName="award_authority_id" placeholder="آمر اعطاء"
                            (selectionChange)="loadContractManager(form.get('award_authority_id').value)">
                    <mat-option *ngFor="let awardAuthority of awardAuthorities"
                                [value]="awardAuthority?.id">
                        {{ awardAuthority?.full_name }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25" *ngIf=" authService.user.role.name === 'award-authority'
                                                || (authService.loggedInUser.role.name === 'cpmd-manager' && contractManagers.length !== 0)
                                                || (authService.loggedInUser.role.name === 'cpmd-system-development' && contractManagers.length !== 0)">
                <mat-select formControlName="contract_manager_id" placeholder="مدیر قرارداد">
                    <mat-option *ngFor="let contractManager of contractManagers"
                                [value]="contractManager?.id">
                        {{ contractManager.full_name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25" *ngIf="authService.loggedInUser.role.name === 'cpmd-manager'
                                                || authService.loggedInUser.role.name === 'cpmd-system-development'">
                <mat-select formControlName="cpm_manager_id" placeholder="آمر نظارت"
                            (selectionChange)="loadSpecialist(form.get('cpm_manager_id').value)">
                    <mat-option *ngFor="let cpmManager of cpmManagers"
                                [value]="cpmManager?.id">
                        {{ cpmManager?.full_name }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25" *ngIf="authService.user.role.name === 'cpmd-manager'
                                                || (authService.loggedInUser.role.name === 'cpmd-manager' && specialists.length !== 0)
                                                || (authService.loggedInUser.role.name === 'cpmd-system-development' && specialists.length !== 0)">
                <mat-select formControlName="specialist_id" placeholder="کارشناس">
                    <mat-option *ngFor="let specialist of specialists"
                                [value]="specialist?.id">
                        {{ specialist.full_name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <mat-select formControlName="amendment_type" placeholder="نوع تعدیل">
                    <mat-option *ngFor="let amendmentType of amendmentTypes"
                                [value]="amendmentType?.id">
                        {{ amendmentType?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="cost_amendment_percentage_start"
                       placeholder="فیصدی تعدیل پولی-از">
                <span matSuffix>%</span>
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="cost_amendment_percentage_end"
                       placeholder="فیصدی تعدیل پولی-الی">
                <span matSuffix>%</span>
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="time_amendment_percentage_start"
                       placeholder="فیصدی تعدیل زمانی-از">
                <span matSuffix>%</span>
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="time_amendment_percentage_end"
                       placeholder="فیصدی تعدیل زمانی-الی">
                <span matSuffix>%</span>
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <mat-select formControlName="donor" placeholder="تمویل کننده">
                    <mat-option *ngFor="let donor of donors"
                                [value]="donor?.id">
                        {{ donor?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <mat-form-field fxFlex="25">
                <mat-select formControlName="zone" placeholder="زون"
                            (selectionChange)="loadProvinces(form.get('zone').value)">
                    <mat-option *ngFor="let zone of zones"
                                [value]="zone?.id">
                        {{ zone?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <mat-select formControlName="province" placeholder="ولایت"
                            (selectionChange)="this.form.get('district').reset()">
                    <mat-option *ngFor="let province of provinces"
                                [value]="province?.id">
                        {{ province?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <npa-auto-complete-input-async
                    *ngIf="form?.get('province').value"
                    fxFlex="31"
                    placeholder="ولسوالی"
                    [params]="{'province_id': form?.get('province').value}"
                    index="name_da"
                    apiUrl="dropDown/specific/district"
                    formControlName="district"
                    [value]="form?.get('district')?.value"
            ></npa-auto-complete-input-async>
            <mat-form-field fxFlex="25">
                <mat-select formControlName="contract_status" placeholder="حالت قرارداد">
                    <mat-option *ngFor="let contractStatus of contractStatuses"
                                [value]="contractStatus?.id">
                        {{ contractStatus?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field fxFlex="25">
                <mat-select formControlName="procurement_method" placeholder="روش تدارکات">
                    <mat-option *ngFor="let procurementMethod of procurementMethods"
                                [value]="procurementMethod?.id">
                        {{ procurementMethod?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="contract_total_value_start"
                       placeholder="قیمت کلی قرارداد-از">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="contract_total_value_end"
                       placeholder="قیمت کلی قرارداد-الی">
            </mat-form-field>

            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="actual_payments_value_start"
                       placeholder="مبلغ پرداخت های حقیقی-از">
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="actual_payments_value_end"
                       placeholder="مبلغ پرداخت های حقیقی-الی">
            </mat-form-field>

            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="actual_payments_physical_percentage_start"
                       placeholder="فیصدی پیشرفت های فزیکی-از">
                <span matSuffix>%</span>
            </mat-form-field>
            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="actual_payments_physical_percentage_end"
                       placeholder="فیصدی پیشرفت های فزیکی-الی">
                <span matSuffix>%</span>
            </mat-form-field>

            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="fiscal_year_start"
                       placeholder="سال مالی-از">
            </mat-form-field>

            <mat-form-field fxFlex='25'>
                <input matInput type="text" formControlName="fiscal_year_end"
                       placeholder="سال مالی-الی">
            </mat-form-field>


            <mat-form-field fxFlex="25" *ngIf="authService.user.role.name === 'cpmd-manager' ||
                                               authService.user.role.name === 'specialist'">
                <mat-select formControlName="sector_id" placeholder="سکتور"
                            (selectionChange)="loadProcurementEntities(form?.get('sector_id').value)">
                    <mat-option *ngFor="let sector of sectors"
                                [value]="sector?.id">
                        {{ sector?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <mat-form-field fxFlex="25" *ngIf="authService.user.role.name === 'cpmd-manager' ||
                                               authService.user.role.name === 'specialist'">
                <mat-select formControlName="procurement_entity_id" placeholder="نهاد تدارکاتی">
                    <mat-option *ngFor="let procurementEntity of procurementEntities"
                                [value]="procurementEntity?.id">
                        {{ procurementEntity?.name_da }}
                    </mat-option>
                </mat-select>
            </mat-form-field>


            <mat-checkbox fxFlex="25" formControlName="above_threshold" (change)="changeToAboveThreshold()">&nbsp;بالا
                تر از صلاحیت آمراعطاء
            </mat-checkbox>
            <mat-checkbox fxFlex="25" formControlName="below_threshold" (change)="changeToBelowThreshold()">پایین تر از
                صلاحیت آمراعطاء
            </mat-checkbox>

        </div>
        <mat-action-row fxLayout="row " fxLayoutAlign="start">
            <div class="search" fxFlex="50">
                <button mat-raised-button color="primary" type="submit">فلتر</button>
                <button type="button"
                        mat-button
                        (click)="this.form.reset()"
                        [disabled]="notificationsService.isLoading">تنظیم مجدد
                </button>
            </div>
        </mat-action-row>

    </mat-expansion-panel>
</form>


<!-------------------------   charts   ------------------------->

<npa-charts-list [data] = "chartData">
    <mat-icon>info</mat-icon>
    در حال بارگزاری چارت ها...
</npa-charts-list>
