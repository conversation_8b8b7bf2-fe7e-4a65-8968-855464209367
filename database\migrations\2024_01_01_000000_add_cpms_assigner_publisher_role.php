<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddCpmsAssignerPublisherRole extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add the new cpms-assigner-publisher role
        DB::table('roles')->insert([
            'name' => 'cpms-assigner-publisher',
            'da_name' => 'آمر اعطا و ناشر قراردادها',
            'can_be_acted' => false,
            'can_act' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Get the role ID for context insertion
        $roleId = DB::table('roles')->where('name', 'cpms-assigner-publisher')->value('id');

        // Add contexts for the new role (combining award-authority and contract-director permissions)
        $contexts = [
            ['name' => 'info-edit', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'info-add', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'edit', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'add', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'exception', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'reports', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'procurement-entity-profile', 'role_id' => $roleId, 'created_at' => now(), 'updated_at' => now()],
        ];

        DB::table('contexts')->insert($contexts);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Get the role ID before deletion
        $roleId = DB::table('roles')->where('name', 'cpms-assigner-publisher')->value('id');
        
        // Delete contexts for this role
        if ($roleId) {
            DB::table('contexts')->where('role_id', $roleId)->delete();
        }
        
        // Delete the role
        DB::table('roles')->where('name', 'cpms-assigner-publisher')->delete();
    }
}
