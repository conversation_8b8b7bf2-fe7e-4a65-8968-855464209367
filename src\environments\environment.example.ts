// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.

import {IRole, IUser} from '../app/services/auth.types';
import {IEnvironment} from './environment.types';

const ROLES: IRole[] = [
    {
        id: 1,
        name: 'contract-manager',
        da_name: 'مدیر قرارداد',
        can_be_acted: true,
        can_act: false,
        contexts: [
            {name: 'exception'},
            {name: 'reports'},
            {name: 'procurement-entity-profile'}
        ]

    },
    {
        id: 2,
        name: 'award-authority',
        da_name: 'آمر اعطا',
        can_be_acted: true,
        can_act: false,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'},
            {name: 'exception'},
            {name: 'reports'},
            {name: 'procurement-entity-profile'}
        ]
    },
    {
        id: 3,
        name: 'specialist',
        da_name: 'کارشناس',
        can_be_acted: true,
        can_act: false,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'},
        ]
    },
    {
        id: 4,
        name: 'cpmd-manager',
        da_name: 'آمر نظارت',
        can_be_acted: true,
        can_act: false,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'}
        ]
    },
    {
        id: 5,
        name: 'company',
        da_name: 'قراردادی',
        can_be_acted: false,
        can_act: false,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'},
            {name: 'exception'},
            {name: 'reports'},
            {name: 'procurement-entity-profile'}
        ]
    },
    // {
    //     id: 6,
    //     name: 'cpmd-director',
    //     da_name: 'ریئس نظارت از پیشرفت قراردادها',
    //     can_be_acted: false,
    //     can_act: true,
    //     contexts: [
    //         {name: 'info-edit'},
    //         {name: 'info-add'},
    //         {name: 'edit'},
    //         {name: 'add'},
    //     ]
    // },
    {
        id: 7,
        name: 'cpmd-system-development',
        da_name: 'آمریت انکشاف سیستم ها',
        can_be_acted: false,
        can_act: true,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'},
        ]
    },
    {
        id: 8,
        name: 'assigner-publisher',
        da_name: 'آمر اعطا و ناشر قراردادها',
        can_be_acted: false,
        can_act: true,
        contexts: [
            {name: 'info-edit'},
            {name: 'info-add'},
            {name: 'edit'},
            {name: 'add'},
            {name: 'exception'},
            {name: 'reports'},
            {name: 'procurement-entity-profile'}
        ]
    },
];
const USER: IUser = {
    id: 8,
    full_name: 'کاربر امتحانی',
    username: 'superuser',
    email: '<EMAIL>',
    role: ROLES[0],
    relatedProcurementEntity: 'Ministry of Finance',
    assignedProjects: ['A', 'B', 'C'],
    position: 'Unknown',

};
const logInData = {
    user: USER,
    roles: ROLES
};


export const environment: IEnvironment = {
    production: false,
    logInData: logInData,
    loginThroughDataBase: false,
    baseUrl: 'http://localhost:8000/',
    frontEndBaseUrl: 'http://localhost:4300/',
    USMBaseURL: 'http://localhost:400/',
    alertPollIntervalInMin: .1,
    specialistMonitoringReportIntervalInMileSecond: 900000,
    isAwsS3Used: true,
    isAlertPollingDisabled: false,
    stickyMessage: {
        shouldShow: true,
        message: 'نسخه آزمایشی'
    }
};
